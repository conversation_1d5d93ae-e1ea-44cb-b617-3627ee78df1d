// Sales Order Weekly Metrics component for MRP Dashboard
export class SalesOrderMetrics {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.salesOrders = [];
    this.weeklyMetrics = [];
    this.weeks = [];
    this.isLoading = false;

    // Database configuration for inventory (for cost and quantity analysis)
    this.inventoryDbName = 'inventoryDb';
    this.inventoryStoreName = 'inventoryItems';
    this.inventoryItems = []; // Cache for inventory data

    // Database configuration for production orders (for WIP calculations)
    this.productionDbName = 'productionDb';
    this.productionStoreName = 'productionOrders';
    this.productionOrders = []; // Cache for production data

    // Week configuration
    this.startDate = new Date(2025, 5, 30); // June 30, 2025 (month is 0-indexed)
    this.endDate = new Date(2025, 11, 29); // December 29, 2025
    
    // Today's date for past due calculations (July 4th, 2025)
    this.todayDate = new Date(2025, 6, 4); // July 4, 2025 (month is 0-indexed)
    
    // Initialize display settings with defaults
    this.displaySettings = {
      showCurrency: true // Default to showing currency symbols
    };
    
    // Past due metrics
    this.pastDueMetrics = {
      orderTotal: 0,
      weeklyWip: 0,
      wipValue: 0,
      onHandValue: 0,
      missingValue: 0,
      ordersCount: 0,
      orders: [],
      projectCogs: 0  // Add project COGs for past due
    };
  }

  // Method to update display settings (called by parent component)
  updateDisplaySettings(newSettings) {
    if (newSettings) {
      this.displaySettings = { ...this.displaySettings, ...newSettings };
      console.log("Updated display settings in SalesOrderMetrics:", this.displaySettings);
    }
  }

  async init() {
    console.log("Initializing Sales Order Weekly Metrics component");
    
    // Inherit display settings from parent component if available
    if (this.parentComponent && this.parentComponent.displaySettings) {
      this.displaySettings = { ...this.parentComponent.displaySettings };
      console.log("Inherited display settings from parent:", this.displaySettings);
    }
    
    this.isLoading = true;
    this.render();

    try {
      // Load supporting data first
      await this.loadInventoryItems();
      await this.loadProductionOrders();

      // Get sales orders data from parent component
      this.salesOrders = this.parentComponent.salesOrders || [];
      
      // Generate weeks and calculate metrics
      this.generateWeeks();
      this.calculateWeeklyMetrics();
      
      this.isLoading = false;
      this.render();
    } catch (error) {
      console.error("Error initializing sales order weekly metrics:", error);
      this.isLoading = false;
      this.showError("Failed to initialize weekly metrics: " + error.message);
    }
  }

  async loadInventoryItems() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.inventoryDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open inventory database for metrics:", event.target.error);
        this.inventoryItems = [];
        resolve(); // Continue without inventory data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.inventoryStoreName)) {
          console.warn("Inventory store not found for metrics");
          this.inventoryItems = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.inventoryStoreName], "readonly");
        const store = transaction.objectStore(this.inventoryStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.inventoryItems = getAllRequest.result;
          console.log(`Loaded ${this.inventoryItems.length} inventory items for metrics`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading inventory items for metrics:", event.target.error);
          this.inventoryItems = [];
          resolve(); // Continue without inventory data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async loadProductionOrders() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.productionDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open production database for metrics:", event.target.error);
        this.productionOrders = [];
        resolve(); // Continue without production data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.productionStoreName)) {
          console.warn("Production store not found for metrics");
          this.productionOrders = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.productionStoreName], "readonly");
        const store = transaction.objectStore(this.productionStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.productionOrders = getAllRequest.result;
          console.log(`Loaded ${this.productionOrders.length} production orders for metrics`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading production orders for metrics:", event.target.error);
          this.productionOrders = [];
          resolve(); // Continue without production data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  generateWeeks() {
    console.log("Generating weeks from June 30, 2025 to December 29, 2025");
    
    this.weeks = [];
    const currentDate = new Date(this.startDate);
    
    while (currentDate <= this.endDate) {
      const weekStart = new Date(currentDate);
      const weekEnd = new Date(currentDate);
      weekEnd.setDate(weekEnd.getDate() + 6); // Add 6 days to get week end
      
      // Format week label
      const weekLabel = `${this.formatDateShort(weekStart)} - ${this.formatDateShort(weekEnd)}`;
      
      this.weeks.push({
        id: `week-${this.weeks.length + 1}`,
        label: weekLabel,
        startDate: new Date(weekStart),
        endDate: new Date(weekEnd),
        weekNumber: this.weeks.length + 1
      });
      
      // Move to next week (add 7 days)
      currentDate.setDate(currentDate.getDate() + 7);
    }
    
    console.log(`Generated ${this.weeks.length} weeks for analysis`);
  }

  calculateWeeklyMetrics() {
    console.log("Calculating weekly metrics for", this.salesOrders.length, "sales orders");
    
    // Initialize weekly metrics
    this.weeklyMetrics = this.weeks.map(week => ({
      week: week,
      orderTotal: 0,
      weeklyWip: 0,
      wipValue: 0,
      onHandValue: 0,
      missingValue: 0,
      ordersCount: 0,
      orders: [],
      projectCogs: 0,
      cumulativeCogs: 0
    }));

    // Calculate past due order counts and totals first (without WIP)
    this.calculatePastDueOrdersOnly();

    // Process each sales order
    this.salesOrders.forEach(order => {
      const weekIndex = this.getWeekIndexForOrder(order);
      
      if (weekIndex !== -1) {
        const weekMetric = this.weeklyMetrics[weekIndex];
        
        // Add order to this week
        weekMetric.orders.push(order);
        weekMetric.ordersCount++;
        weekMetric.orderTotal += order.OrderTotal || 0;
        
        // Calculate project cost using the same logic as sales_order_usage.js
        const flatItems = this.buildFlatItemsSummary(order);
        const projectCost = flatItems.reduce((sum, item) => sum + (item.totalQuantity * item.lastCost), 0);
        weekMetric.projectCogs += projectCost;
        
        // Calculate inventory metrics for this order (for On Hand and Missing values only)
        const orderMetrics = this.calculateOrderMetrics(order);
        weekMetric.onHandValue += orderMetrics.onHandValue;
        weekMetric.missingValue += orderMetrics.missingValue;
      }
    });

    // Past Due WIP Value should be the sum of ALL order totals (Past Due + ALL weekly totals)
    // This is the complete total pipeline value
    this.pastDueMetrics.wipValue = this.pastDueMetrics.orderTotal + this.weeklyMetrics.reduce((sum, week) => sum + week.orderTotal, 0);

    // Now calculate cascading WIP Values for each week
    // Week 1 WIP Value = Past Due WIP Value - Past Due Order Total
    // Week N WIP Value = Previous Week WIP Value - Previous Week Order Total
    this.weeklyMetrics.forEach((weekMetric, index) => {
      if (index === 0) {
        // Week 1: Base on Past Due
        weekMetric.wipValue = this.pastDueMetrics.wipValue - this.pastDueMetrics.orderTotal;
      } else {
        // Week N: Base on previous week
        const prevWeek = this.weeklyMetrics[index - 1];
        weekMetric.wipValue = prevWeek.wipValue - prevWeek.orderTotal;
      }
    });

    // Calculate Weekly WIP as the difference between current and NEXT WIP values
    // Past Due Weekly WIP = Past Due WIP Value - Week 1 WIP Value
    // Week N Weekly WIP = Week N WIP Value - Week (N+1) WIP Value
    
    // First calculate Past Due Weekly WIP
    if (this.weeklyMetrics.length > 0) {
      this.pastDueMetrics.weeklyWip = this.pastDueMetrics.wipValue - this.weeklyMetrics[0].wipValue;
    } else {
      this.pastDueMetrics.weeklyWip = 0;
    }
    
    // Then calculate each week's Weekly WIP
    this.weeklyMetrics.forEach((weekMetric, index) => {
      if (index < this.weeklyMetrics.length - 1) {
        // Week N: Difference from next week
        const nextWeek = this.weeklyMetrics[index + 1];
        weekMetric.weeklyWip = weekMetric.wipValue - nextWeek.wipValue;
      } else {
        // Last week: WIP Value itself (no next week to subtract)
        weekMetric.weeklyWip = weekMetric.wipValue;
      }
    });

    // Calculate cascading Total Cumulative COGs (sum from Past Due Project COGs)
    // Past Due Total Cumulative COGs = Past Due Total Project COGs
    // Week 1 Total Cumulative COGs = Past Due Total Cumulative COGs + Week 1 Total Project COGs
    // Week N Total Cumulative COGs = Week (N-1) Total Cumulative COGs + Week N Total Project COGs
    this.weeklyMetrics.forEach((weekMetric, index) => {
      if (index === 0) {
        // Week 1: Base on Past Due
        weekMetric.cumulativeCogs = this.pastDueMetrics.projectCogs + weekMetric.projectCogs;
      } else {
        // Week N: Base on previous week
        const prevWeek = this.weeklyMetrics[index - 1];
        weekMetric.cumulativeCogs = prevWeek.cumulativeCogs + weekMetric.projectCogs;
      }
    });

    console.log("Weekly metrics calculated:", this.weeklyMetrics.length, "weeks processed");
    console.log("Past due metrics calculated:", this.pastDueMetrics.ordersCount, "past due orders");
  }

  calculatePastDueOrdersOnly() {
    // Reset past due metrics
    this.pastDueMetrics = {
      orderTotal: 0,
      weeklyWip: 0,
      wipValue: 0,
      onHandValue: 0,
      missingValue: 0,
      ordersCount: 0,
      orders: [],
      projectCogs: 0  // Add project COGs for past due
    };

    console.log("Calculating past due orders. Today's date:", this.todayDate);
    
    // Find all orders with shipping date before today (July 4th, 2025)
    this.salesOrders.forEach((order, index) => {
      if (this.isOrderPastDue(order)) {
        // Calculate project cost using the same logic as sales_order_usage.js
        const flatItems = this.buildFlatItemsSummary(order);
        const projectCost = flatItems.reduce((sum, item) => sum + (item.totalQuantity * item.lastCost), 0);
        
        this.pastDueMetrics.orders.push(order);
        this.pastDueMetrics.ordersCount++;
        this.pastDueMetrics.orderTotal += order.OrderTotal || 0;
        this.pastDueMetrics.projectCogs += projectCost;
      }
    });
    
    console.log("Past due calculation complete:", {
      pastDueCount: this.pastDueMetrics.ordersCount,
      pastDueOrderTotal: this.pastDueMetrics.orderTotal,
      pastDueProjectCogs: this.pastDueMetrics.projectCogs
    });
    
    // On Hand and Missing values remain 0 for past due orders
    this.pastDueMetrics.onHandValue = 0;
    this.pastDueMetrics.missingValue = 0;
  }

  isOrderPastDue(order) {
    if (!order.ShippingDate || !order.ShippingDate.year) {
      return false; // No shipping date, not past due
    }

    try {
      const shipDate = new Date(
        order.ShippingDate.year,
        order.ShippingDate.month - 1, // month is 0-indexed in JS Date
        order.ShippingDate.day
      );

      // Check if shipping date is before today (July 4th, 2025)
      return shipDate < this.todayDate;
    } catch (error) {
      console.error("Error parsing shipping date for past due check", order.OrderNbr, error);
      return false;
    }
  }

  getWeekIndexForOrder(order) {
    if (!order.ShippingDate || !order.ShippingDate.year) {
      return -1; // No shipping date
    }

    try {
      const shipDate = new Date(
        order.ShippingDate.year,
        order.ShippingDate.month - 1, // month is 0-indexed in JS Date
        order.ShippingDate.day
      );

      // Find which week this date falls into
      for (let i = 0; i < this.weeks.length; i++) {
        const week = this.weeks[i];
        if (shipDate >= week.startDate && shipDate <= week.endDate) {
          return i;
        }
      }
    } catch (error) {
      console.error("Error parsing shipping date for order", order.OrderNbr, error);
    }

    return -1; // Date doesn't fall in our week range
  }

  calculateOrderMetrics(order) {
    // Get flat items summary (similar to SalesOrderUsage logic)
    const flatItems = this.buildFlatItemsSummary(order);
    
    let wipValue = 0;
    let onHandValue = 0;
    let missingValue = 0;

    flatItems.forEach(item => {
      const itemTotalValue = item.totalQuantity * item.lastCost;
      const itemOnHandValue = item.qtyOnHand * item.lastCost;
      const itemMissingValue = item.missing * item.lastCost;

      // For WIP calculation, we'll consider items that are in production
      // For now, we'll calculate WIP as the difference between total needed and on hand
      const itemWipValue = Math.max(0, itemTotalValue - itemOnHandValue - itemMissingValue);

      wipValue += itemWipValue;
      onHandValue += itemOnHandValue;
      missingValue += itemMissingValue;
    });

    return {
      wipValue,
      onHandValue,
      missingValue
    };
  }

  buildFlatItemsSummary(salesOrder) {
    const flatItemsMap = new Map();
    
    // Process each line item (similar to SalesOrderUsage logic)
    if (Array.isArray(salesOrder.LineItems)) {
      salesOrder.LineItems.forEach(lineItem => {
        // Check if this line item has production orders
        const hasProduction = lineItem.BOMItems && lineItem.BOMItems.some(bom => 
          bom.ProductionNbr && bom.ProductionNbr.trim()
        );
        
        if (hasProduction) {
          // Get materials from production orders
          const processedProductionNbrs = new Set();
          
          lineItem.BOMItems.forEach(bomItem => {
            if (bomItem.ProductionNbr && bomItem.ProductionNbr.trim() && 
                !processedProductionNbrs.has(bomItem.ProductionNbr.trim())) {
              processedProductionNbrs.add(bomItem.ProductionNbr.trim());
              
              // Find the production order
              const productionOrder = this.productionOrders.find(po => 
                po.MainProductionNbr === bomItem.ProductionNbr.trim()
              );
              
              if (productionOrder && Array.isArray(productionOrder.Materials)) {
                productionOrder.Materials.forEach(material => {
                  const inventoryId = material.InventoryID;
                  const quantity = material.QtyRequired || 0;
                  
                  if (inventoryId && inventoryId.trim()) {
                    const key = inventoryId.trim();
                    if (flatItemsMap.has(key)) {
                      flatItemsMap.get(key).totalQuantity += quantity;
                    } else {
                      flatItemsMap.set(key, {
                        inventoryId: key,
                        description: material.Description || '',
                        totalQuantity: quantity,
                        uom: material.UOM || ''
                      });
                    }
                  }
                });
              } else {
                // If production order not found, fall back to line item
                const inventoryId = lineItem.InventoryID;
                const quantity = lineItem.Quantity || 0;
                
                if (inventoryId && inventoryId.trim()) {
                  const key = inventoryId.trim();
                  if (flatItemsMap.has(key)) {
                    flatItemsMap.get(key).totalQuantity += quantity;
                  } else {
                    flatItemsMap.set(key, {
                      inventoryId: key,
                      description: lineItem.LineDescription || '',
                      totalQuantity: quantity,
                      uom: lineItem.UOM || ''
                    });
                  }
                }
              }
            }
          });
        } else {
          // No production - use the line item itself
          const inventoryId = lineItem.InventoryID;
          const quantity = lineItem.Quantity || 0;
          
          if (inventoryId && inventoryId.trim()) {
            const key = inventoryId.trim();
            if (flatItemsMap.has(key)) {
              flatItemsMap.get(key).totalQuantity += quantity;
            } else {
              flatItemsMap.set(key, {
                inventoryId: key,
                description: lineItem.LineDescription || '',
                totalQuantity: quantity,
                uom: lineItem.UOM || ''
              });
            }
          }
        }
      });
    }
    
    // Convert Map to array, add inventory info, and sort by inventory ID
    const flatItemsArray = Array.from(flatItemsMap.values())
      .map(item => {
        const inventoryInfo = this.getInventoryInfo(item.inventoryId);
        
        return {
          ...item,
          lastCost: inventoryInfo.lastCost,
          qtyOnHand: inventoryInfo.qtyOnHand,
          missing: Math.max(0, item.totalQuantity - inventoryInfo.qtyOnHand),
          found: inventoryInfo.found
        };
      })
      .sort((a, b) => a.inventoryId.localeCompare(b.inventoryId));
    
    return flatItemsArray;
  }

  getInventoryInfo(inventoryId) {
    if (!inventoryId || !this.inventoryItems.length) {
      return {
        lastCost: 0,
        qtyOnHand: 0,
        found: false
      };
    }

    const inventoryItem = this.inventoryItems.find(item => 
      item.InventoryID === inventoryId.trim()
    );

    if (inventoryItem) {
      return {
        lastCost: inventoryItem.LastCost || 0,
        qtyOnHand: inventoryItem.QtyOnHand || 0,
        found: true
      };
    }

    return {
      lastCost: 0,
      qtyOnHand: 0,
      found: false
    };
  }

  refresh() {
    console.log("Refreshing Sales Order Weekly Metrics");
    
    // Re-inherit display settings from parent component
    if (this.parentComponent && this.parentComponent.displaySettings) {
      this.displaySettings = { ...this.parentComponent.displaySettings };
      console.log("Refreshed display settings from parent:", this.displaySettings);
    }
    
    this.init();
  }

  render() {
    if (!this.container) {
      console.error("No container available for Sales Order Metrics");
      return;
    }

    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Calculating weekly metrics...</p>
      </div>
    `;
  }

  renderContent() {
    if (this.salesOrders.length === 0) {
      this.renderEmptyState();
      return;
    }

    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
          
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Week
                </th>
                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Orders
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Order Total
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Weekly WIP
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  WIP Value
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  On Hand Value
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Missing Value
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total Project COGs
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total Cumulative COGs
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderPastDueRow()}
              ${this.renderWeeklyRows()}
            </tbody>
          </table>
        </div>
          
        <!-- Summary Row -->
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-t border-gray-200 dark:border-gray-600">
          <div class="grid grid-cols-9 gap-4 text-sm">
            <div class="text-gray-900 dark:text-white font-medium">
              Total (${this.weeks.length} weeks + Past Due)
            </div>
            <div class="text-center text-gray-900 dark:text-white">
              ${this.pastDueMetrics.ordersCount + this.weeklyMetrics.reduce((sum, w) => sum + w.ordersCount, 0)}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.orderTotal + this.weeklyMetrics.reduce((sum, w) => sum + w.orderTotal, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.weeklyWip + this.weeklyMetrics.reduce((sum, w) => sum + w.weeklyWip, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.wipValue + this.weeklyMetrics.reduce((sum, w) => sum + w.wipValue, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.onHandValue + this.weeklyMetrics.reduce((sum, w) => sum + w.onHandValue, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.missingValue + this.weeklyMetrics.reduce((sum, w) => sum + w.missingValue, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.projectCogs + this.weeklyMetrics.reduce((sum, w) => sum + w.projectCogs, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.weeklyMetrics.length > 0 ? this.weeklyMetrics[this.weeklyMetrics.length - 1].cumulativeCogs : this.pastDueMetrics.projectCogs)}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderPastDueRow() {
    return `
      <tr class="bg-red-50 dark:bg-red-900 hover:bg-red-100 dark:hover:bg-red-800 border-b-2 border-red-200 dark:border-red-700">
        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
          <div>
            <div class="font-medium text-red-700 dark:text-red-300">Past Due</div>
            <div class="text-xs text-red-600 dark:text-red-400">Before July 4, 2025</div>
          </div>
        </td>
        <td class="px-4 py-3 text-center text-sm text-gray-900 dark:text-white">
          <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${this.pastDueMetrics.ordersCount > 0 ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'} rounded-full">
            ${this.pastDueMetrics.ordersCount}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.orderTotal)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.weeklyWip)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.wipValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-500 dark:text-gray-400">
          -
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-500 dark:text-gray-400">
          -
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.projectCogs)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.projectCogs)}
          </span>
        </td>
      </tr>
    `;
  }

  renderWeeklyRows() {
    return this.weeklyMetrics.map((weekMetric, index) => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
          <div>
            <div class="font-medium">Week ${weekMetric.week.weekNumber}</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">${weekMetric.week.label}</div>
          </div>
        </td>
        <td class="px-4 py-3 text-center text-sm text-gray-900 dark:text-white">
          <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${weekMetric.ordersCount > 0 ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'}">
            ${weekMetric.ordersCount}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          ${this.formatCurrency(weekMetric.orderTotal)}
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.weeklyWip > 0 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}">
            ${this.formatCurrency(weekMetric.weeklyWip)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.wipValue > 0 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}">
            ${this.formatCurrency(weekMetric.wipValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.onHandValue > 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'}">
            ${this.formatCurrency(weekMetric.onHandValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.missingValue > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}">
            ${this.formatCurrency(weekMetric.missingValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.projectCogs > 0 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}">
            ${this.formatCurrency(weekMetric.projectCogs)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-blue-600 dark:text-blue-400">
            ${this.formatCurrency(weekMetric.cumulativeCogs)}
          </span>
        </td>
      </tr>
    `).join('');
  }

  renderEmptyState() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="text-gray-600 dark:text-gray-400">
          <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <p class="text-center text-lg font-medium mb-2">No sales order data available</p>
          <p class="text-center text-sm">Load sales orders to view weekly metrics analysis</p>
        </div>
      </div>
    `;
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return this.displaySettings.showCurrency ? '$0.00' : '0.00';
    }
    
    if (this.displaySettings.showCurrency) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'CAD',
        minimumFractionDigits: 2
      }).format(amount);
    } else {
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    }
  }

  formatDateShort(date) {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  }

  showError(message) {
    console.error("Sales Order Weekly Metrics Error:", message);
    
    if (this.container) {
      this.container.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    }
  }



  // Export functionality for metrics data
  exportToCSV() {
    if (this.weeklyMetrics.length === 0 && this.pastDueMetrics.ordersCount === 0) {
      this.showError('No metrics data to export');
      return;
    }

    try {
      // Prepare CSV headers
      const headers = [
        'Week',
        'Orders Count',
        'Order Total',
        'Weekly WIP',
        'WIP Value',
        'On Hand Value',
        'Missing Value',
        'Total Project COGs',
        'Total Cumulative COGs'
      ];

      // Prepare CSV rows - start with Past Due
      const rows = [];
      
      // Add Past Due row
      rows.push([
        'Past Due (Before July 4, 2025)',
        this.pastDueMetrics.ordersCount,
        this.formatNumberForExport(this.pastDueMetrics.orderTotal),
        this.formatNumberForExport(this.pastDueMetrics.weeklyWip),
        this.formatNumberForExport(this.pastDueMetrics.wipValue),
        this.formatNumberForExport(this.pastDueMetrics.onHandValue),
        this.formatNumberForExport(this.pastDueMetrics.missingValue),
        this.formatNumberForExport(this.pastDueMetrics.projectCogs),
        this.formatNumberForExport(this.pastDueMetrics.projectCogs)
      ]);

      // Add weekly metrics rows
      this.weeklyMetrics.forEach(weekMetric => {
        rows.push([
          `Week ${weekMetric.week.weekNumber} (${weekMetric.week.label})`,
          weekMetric.ordersCount,
          this.formatNumberForExport(weekMetric.orderTotal),
          this.formatNumberForExport(weekMetric.weeklyWip),
          this.formatNumberForExport(weekMetric.wipValue),
          this.formatNumberForExport(weekMetric.onHandValue),
          this.formatNumberForExport(weekMetric.missingValue),
          this.formatNumberForExport(weekMetric.projectCogs),
          this.formatNumberForExport(weekMetric.cumulativeCogs)
        ]);
      });

      // Add totals row
      const totalOrders = this.pastDueMetrics.ordersCount + this.weeklyMetrics.reduce((sum, w) => sum + w.ordersCount, 0);
      const totalOrderTotal = this.pastDueMetrics.orderTotal + this.weeklyMetrics.reduce((sum, w) => sum + w.orderTotal, 0);
      const totalWeeklyWip = this.pastDueMetrics.weeklyWip + this.weeklyMetrics.reduce((sum, w) => sum + w.weeklyWip, 0);
      const totalWipValue = this.pastDueMetrics.wipValue + this.weeklyMetrics.reduce((sum, w) => sum + w.wipValue, 0);
      const totalOnHandValue = this.pastDueMetrics.onHandValue + this.weeklyMetrics.reduce((sum, w) => sum + w.onHandValue, 0);
      const totalMissingValue = this.pastDueMetrics.missingValue + this.weeklyMetrics.reduce((sum, w) => sum + w.missingValue, 0);
      const totalProjectCogs = this.pastDueMetrics.projectCogs + this.weeklyMetrics.reduce((sum, w) => sum + w.projectCogs, 0);
      const finalCumulativeCogs = this.weeklyMetrics.length > 0 ? this.weeklyMetrics[this.weeklyMetrics.length - 1].cumulativeCogs : this.pastDueMetrics.projectCogs;

      rows.push([
        `TOTAL (${this.weeklyMetrics.length} weeks + Past Due)`,
        totalOrders,
        this.formatNumberForExport(totalOrderTotal),
        this.formatNumberForExport(totalWeeklyWip),
        this.formatNumberForExport(totalWipValue),
        this.formatNumberForExport(totalOnHandValue),
        this.formatNumberForExport(totalMissingValue),
        this.formatNumberForExport(totalProjectCogs),
        this.formatNumberForExport(finalCumulativeCogs)
      ]);

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(field => {
          // Escape fields that contain commas, quotes, or line breaks
          const fieldStr = String(field || '');
          if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
            return `"${fieldStr.replace(/"/g, '""')}"`;
          }
          return fieldStr;
        }).join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_order_metrics_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`Exported Sales Order Metrics to CSV: ${this.weeklyMetrics.length} weeks + Past Due`);
      
      // Show success message
      this.showSuccess(`Exported ${this.weeklyMetrics.length + 1} rows of metrics data to CSV`);
    } catch (error) {
      console.error('Error exporting metrics to CSV:', error);
      this.showError('Failed to export metrics data: ' + error.message);
    }
  }

  // Format numbers for export without currency symbols
  formatNumberForExport(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return '0.00';
    }
    
    return amount.toFixed(2);
  }

  showSuccess(message) {
    console.log("Sales Order Metrics Success:", message);
    
    // Create temporary success message
    const successElement = document.createElement('div');
    successElement.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
    successElement.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <span class="block sm:inline">${message}</span>
        </div>
        <div class="ml-auto pl-3">
          <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-green-500 hover:text-green-700">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(successElement);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (successElement.parentElement) {
        successElement.parentElement.removeChild(successElement);
      }
    }, 5000);
  }
} 