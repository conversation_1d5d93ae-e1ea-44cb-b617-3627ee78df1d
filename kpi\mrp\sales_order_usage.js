// Sales Order Usage Analysis component for MRP Dashboard
export class SalesOrderUsage {
  constructor(container, parentComponent) {
    console.log("🏗️ SalesOrderUsage Constructor");
    console.log("Container received:", !!container);
    console.log("Parent component received:", !!parentComponent);
    console.log("Parent component type:", parentComponent?.constructor?.name);
    console.log("Parent component sales orders:", parentComponent?.salesOrders?.length || 0);
    
    this.container = container;
    this.parentComponent = parentComponent;
    this.salesOrdersData = [];
    this.filteredOrders = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'OrderNbr';
    this.sortDirection = 'desc';
    this.filterStatus = 'all';
    this.isLoading = false;

    // Database configuration for inventory (for cost analysis)
    this.inventoryDbName = 'inventoryDb';
    this.inventoryStoreName = 'inventoryItems';
    this.inventoryItems = []; // Cache for inventory data

    // Initialize display settings with defaults
    this.displaySettings = {
      showCurrency: true // Default to showing currency symbols
    };

    console.log("🏗️ SalesOrderUsage Constructor Complete");
  }

  // Method to update display settings (called by parent component)
  updateDisplaySettings(newSettings) {
    if (newSettings) {
      this.displaySettings = { ...this.displaySettings, ...newSettings };
      console.log("Updated display settings in SalesOrderUsage:", this.displaySettings);
    }
  }

  async init() {
    console.log("=== SALES ORDER USAGE INIT STARTED ===");
    console.log("Container:", this.container);
    console.log("Parent component:", this.parentComponent);
    console.log("Parent component type:", this.parentComponent?.constructor?.name);
    console.log("Parent sales orders count:", this.parentComponent?.salesOrders?.length || 0);

    await this.refresh();
  }

  async refresh() {
    console.log("=== REFRESHING SALES ORDER USAGE ===");

    this.isLoading = true;
    this.render();

    try {
      console.log("Loading inventory data for cost analysis...");
      await this.loadInventoryItems();
      
      console.log("Building sales orders table...");
      await this.buildSalesOrdersTable();

      this.isLoading = false;
      this.render();
      console.log("=== SALES ORDER USAGE REFRESH COMPLETED ===");
    } catch (error) {
      console.error("=== ERROR IN SALES ORDER USAGE REFRESH ===");
      console.error("Error refreshing sales orders:", error);
      console.error("Stack trace:", error.stack);
      this.isLoading = false;
      this.showError("Failed to refresh sales orders: " + error.message);
      this.render();
    }
  }

  async loadInventoryItems() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.inventoryDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open inventory database for cost analysis:", event.target.error);
        this.inventoryItems = [];
        resolve(); // Continue without inventory data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.inventoryStoreName)) {
          console.warn("Inventory store not found, continuing without inventory data");
          this.inventoryItems = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.inventoryStoreName], "readonly");
        const store = transaction.objectStore(this.inventoryStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.inventoryItems = getAllRequest.result;
          console.log(`Loaded ${this.inventoryItems.length} inventory items for cost analysis`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading inventory items:", event.target.error);
          this.inventoryItems = [];
          resolve(); // Continue without inventory data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async buildSalesOrdersTable() {
    try {
      console.log("=== STARTING buildSalesOrdersTable ===");
      console.log(`Sales orders available: ${this.parentComponent.salesOrders?.length || 0}`);

      if (!this.parentComponent.salesOrders || this.parentComponent.salesOrders.length === 0) {
        console.warn("⚠️ No sales orders available for table");
        this.salesOrdersData = [];
        this.filteredOrders = [];
        this.calculateTotalPages();
        return;
      }

      // Build sales orders data with cost analysis
      console.log("🚀 Building sales orders with cost analysis...");
      const salesOrdersWithCosts = this.buildSalesOrdersWithCosts();
      
      this.salesOrdersData = salesOrdersWithCosts;
      this.filteredOrders = [...this.salesOrdersData];
      this.calculateTotalPages();
      this.applyFilters();

      console.log(`✅ Built sales orders table with ${this.salesOrdersData.length} orders`);

    } catch (error) {
      console.error("❌ Error building sales orders table:", error);
      console.error("Stack trace:", error.stack);
      this.salesOrdersData = [];
      this.filteredOrders = [];
    }
  }

  buildSalesOrdersWithCosts() {
    console.log("🔨 Building sales orders with cost analysis...");
    
    // Process each sales order and calculate costs
    return this.parentComponent.salesOrders.map((salesOrder) => {
      console.log(`📋 Processing sales order: ${salesOrder.OrderNbr}`);
      
      // Build flat items summary for cost calculation
      const flatItems = this.buildFlatItemsSummary(salesOrder);
      
      // Calculate totals
      const projectCost = flatItems.reduce((sum, item) => sum + (item.totalQuantity * item.lastCost), 0);
      const onHandValue = flatItems.reduce((sum, item) => sum + (item.qtyOnHand * item.lastCost), 0);
      const missingValue = flatItems.reduce((sum, item) => sum + (item.missing * item.lastCost), 0);
      
      return {
        id: salesOrder.id,
        OrderNbr: salesOrder.OrderNbr,
        Status: salesOrder.Status,
        OrderTotal: salesOrder.OrderTotal || 0,
        ProjectCost: projectCost,
        OnHandValue: onHandValue,
        MissingValue: missingValue,
        ShipDate: salesOrder.ShippingDate,
        LineItemCount: salesOrder.LineItemCount || 0,
        FlatItemsCount: flatItems.length
      };
         });
   }

  getInventoryInfo(inventoryId) {
    if (!inventoryId || !this.inventoryItems.length) {
      return {
        lastCost: 0,
        qtyOnHand: 0,
        found: false
      };
    }

    const inventoryItem = this.inventoryItems.find(item => 
      item.InventoryID === inventoryId.trim()
    );

    if (inventoryItem) {
      return {
        lastCost: inventoryItem.LastCost || 0,
        qtyOnHand: inventoryItem.QtyOnHand || 0,
        found: true
      };
    }

    return {
      lastCost: 0,
      qtyOnHand: 0,
      found: false
    };
  }

  buildFlatItemsSummary(salesOrder) {
    console.log(`📊 Building flat items summary for order: ${salesOrder.OrderNbr}`);
    
    const flatItemsMap = new Map(); // Use Map to track quantities
    
    // Process each line item
    if (Array.isArray(salesOrder.LineItems)) {
      salesOrder.LineItems.forEach(lineItem => {
        // Check if this line item has production orders
        const hasProduction = lineItem.BOMItems && lineItem.BOMItems.some(bom => 
          bom.ProductionNbr && bom.ProductionNbr.trim()
        );
        
        if (hasProduction) {
          // For now, just use the line item itself when it has production
          // (We would need production order data to get actual materials)
          const inventoryId = lineItem.InventoryID;
          const quantity = lineItem.Quantity || 0;
          
          if (inventoryId && inventoryId.trim()) {
            const key = inventoryId.trim();
            if (flatItemsMap.has(key)) {
              flatItemsMap.get(key).totalQuantity += quantity;
            } else {
              flatItemsMap.set(key, {
                inventoryId: key,
                description: lineItem.LineDescription || '',
                totalQuantity: quantity,
                uom: lineItem.UOM || ''
              });
            }
          }
        } else {
          // No production - use the line item itself
          const inventoryId = lineItem.InventoryID;
          const quantity = lineItem.Quantity || 0;
          
          if (inventoryId && inventoryId.trim()) {
            const key = inventoryId.trim();
            if (flatItemsMap.has(key)) {
              flatItemsMap.get(key).totalQuantity += quantity;
            } else {
              flatItemsMap.set(key, {
                inventoryId: key,
                description: lineItem.LineDescription || '',
                totalQuantity: quantity,
                uom: lineItem.UOM || ''
              });
            }
          }
        }
      });
    }
    
    // Convert Map to array, add inventory info, and sort by inventory ID
    const flatItemsArray = Array.from(flatItemsMap.values())
      .map(item => {
        // Add inventory VLOOKUP info
        const inventoryInfo = this.getInventoryInfo(item.inventoryId);
        
        return {
          ...item,
          lastCost: inventoryInfo.lastCost,
          qtyOnHand: inventoryInfo.qtyOnHand,
          missing: Math.max(0, item.totalQuantity - inventoryInfo.qtyOnHand),
          found: inventoryInfo.found
        };
      })
      .sort((a, b) => a.inventoryId.localeCompare(b.inventoryId));
    
    console.log(`📊 Found ${flatItemsArray.length} unique flat items for order ${salesOrder.OrderNbr}`);
    return flatItemsArray;
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredOrders.length / this.itemsPerPage));

    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  applyFilters() {
    let filtered = [...this.salesOrdersData];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(order => {
        return (
          (order.OrderNbr || '').toLowerCase().includes(searchLower) ||
          (order.Status || '').toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply status filter
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(order => order.Status === this.filterStatus);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (this.sortField) {
        case 'OrderNbr':
        case 'Status':
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
          break;
        case 'OrderTotal':
        case 'ProjectCost':
        case 'OnHandValue':
        case 'MissingValue':
        case 'LineItemCount':
        case 'FlatItemsCount':
          aValue = a[this.sortField] || 0;
          bValue = b[this.sortField] || 0;
          break;
        case 'ShipDate':
          // Handle date objects
          if (a.ShipDate && a.ShipDate.year) {
            aValue = new Date(a.ShipDate.year, a.ShipDate.month - 1, a.ShipDate.day);
          } else {
            aValue = new Date(0);
          }
          if (b.ShipDate && b.ShipDate.year) {
            bValue = new Date(b.ShipDate.year, b.ShipDate.month - 1, b.ShipDate.day);
          } else {
            bValue = new Date(0);
          }
          break;
        default:
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
      }

      // Handle different data types
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else if (aValue instanceof Date && bValue instanceof Date) {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // String comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredOrders = filtered;
    this.calculateTotalPages();
    this.currentPage = 1; // Reset to first page when filtering
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Analyzing usage data...</p>
      </div>
    `;
  }

  renderContent() {
    console.log("Rendering sales orders content:");
    console.log("- Total sales orders:", this.salesOrdersData.length);
    console.log("- Filtered orders:", this.filteredOrders.length);
    console.log("- Parent sales orders:", this.parentComponent.salesOrders ? this.parentComponent.salesOrders.length : 0);

    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div id="sales-usage-table-container">
          <!-- Sales Orders Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderNbr">
                    Order # <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Status">
                    Status <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderTotal">
                    Order Total <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ProjectCost">
                    Project Cost <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OnHandValue">
                    On Hand <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="MissingValue">
                    Missing <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ShipDate">
                    Ship Date <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${this.renderTableRows()}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredOrders.length)} to 
              ${Math.min(this.currentPage * this.itemsPerPage, this.filteredOrders.length)} of 
              ${this.filteredOrders.length} results
            </div>
            
            <div class="flex items-center space-x-1">
              <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
              </button>
              <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
              </button>
              
              <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
                ${this.currentPage} of ${this.totalPages}
              </span>
              
              <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
              </button>
              <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    this.setupEventListeners();
  }

  renderTableRows() {
    if (this.filteredOrders.length === 0) {
      return `
        <tr>
          <td colspan="8" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
            No sales orders found. Load sales orders first to view cost analysis.
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredOrders.length);
    const displayedOrders = this.filteredOrders.slice(start, end);

    return displayedOrders.map(order => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
          ${this.escapeHtml(order.OrderNbr || '')}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-center">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(order.Status || '')}">
            ${this.escapeHtml(order.Status || '')}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${this.formatCurrency(order.OrderTotal)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="font-medium text-purple-600 dark:text-purple-400">${this.formatCurrency(order.ProjectCost)}</span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="font-medium text-green-600 dark:text-green-400">${this.formatCurrency(order.OnHandValue)}</span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="font-medium ${order.MissingValue > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}">${this.formatCurrency(order.MissingValue)}</span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(order.ShipDate)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button data-id="${order.id}" class="view-order text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
            <i class="fas fa-eye"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }

  setupEventListeners() {
    // Add a small delay to ensure DOM is fully rendered
    setTimeout(() => {
      // Search input
      const searchInput = document.getElementById('sales-usage-search');
      if (searchInput) {
        searchInput.addEventListener('input', this.debounce(() => {
          this.searchTerm = searchInput.value.trim();
          this.currentPage = 1;
          this.applyFilters();
        }, 300));
      }

      // Clear search
      const clearSearchBtn = document.getElementById('clear-search');
      if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', () => {
          this.searchTerm = '';
          if (searchInput) searchInput.value = '';
          this.currentPage = 1;
          this.applyFilters();
        });
      }

      // Status filter
      const statusFilter = document.getElementById('order-status-filter');
      if (statusFilter) {
        statusFilter.addEventListener('change', () => {
          this.filterStatus = statusFilter.value;
          this.currentPage = 1;
          this.applyFilters();
        });
      }

      // Export button
      const exportButton = document.getElementById('export-button');
      if (exportButton) {
        exportButton.replaceWith(exportButton.cloneNode(true));
        const freshExportButton = document.getElementById('export-button');
        if (freshExportButton) {
          freshExportButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.exportSalesOrdersData();
          });
        }
      }

      // Sort headers
      const sortHeaders = document.querySelectorAll('th[data-sort]');
      sortHeaders.forEach(header => {
        header.addEventListener('click', () => {
          const field = header.getAttribute('data-sort');
          if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
          } else {
            this.sortField = field;
            this.sortDirection = 'asc';
          }
          this.applyFilters();
        });
      });

      // Pagination event handlers
      const firstPageBtn = document.getElementById('first-page');
      if (firstPageBtn) {
        firstPageBtn.addEventListener('click', () => {
          if (this.currentPage > 1) {
            this.currentPage = 1;
            this.render();
          }
        });
      }

      const prevPageBtn = document.getElementById('prev-page');
      if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => {
          if (this.currentPage > 1) {
            this.currentPage--;
            this.render();
          }
        });
      }

      const nextPageBtn = document.getElementById('next-page');
      if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => {
          if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.render();
          }
        });
      }

      const lastPageBtn = document.getElementById('last-page');
      if (lastPageBtn) {
        lastPageBtn.addEventListener('click', () => {
          if (this.currentPage < this.totalPages) {
            this.currentPage = this.totalPages;
            this.render();
          }
        });
      }

      // View order buttons
      const viewButtons = document.querySelectorAll('.view-order');
      viewButtons.forEach(button => {
        button.addEventListener('click', (event) => {
          event.preventDefault();
          event.stopPropagation();
          const orderId = button.getAttribute('data-id');
          this.viewOrderDetails(orderId);
        });
      });

      console.log('All event listeners have been set up');
    }, 50);
  }

  exportSalesOrdersData() {
    if (this.filteredOrders.length === 0) {
      alert('No sales orders data to export');
      return;
    }

    try {
      // Prepare CSV headers
      const headers = [
        'Order Number',
        'Status',
        'Order Total',
        'Project Cost',
        'On Hand Value',
        'Missing Value',
        'Ship Date',
        'Line Items',
        'Flat Items'
      ];

      // Prepare CSV rows
      const rows = this.filteredOrders.map(order => [
        order.OrderNbr,
        order.Status,
        order.OrderTotal.toFixed(2),
        order.ProjectCost.toFixed(2),
        order.OnHandValue.toFixed(2),
        order.MissingValue.toFixed(2),
        this.formatDate(order.ShipDate),
        order.LineItemCount,
        order.FlatItemsCount
      ]);

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_orders_cost_analysis_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`Exported ${this.filteredOrders.length} sales orders to CSV`);
    } catch (error) {
      console.error('Error exporting sales orders data:', error);
      alert('Failed to export data: ' + error.message);
    }
  }

  viewOrderDetails(orderId) {
    const order = this.salesOrdersData.find(o => o.id === orderId);
    if (!order) {
      console.error('Order not found:', orderId);
      return;
    }

    console.log('Viewing order details for:', order.OrderNbr);
    // For now, just log the order details
    // In a full implementation, this would show a modal with detailed cost breakdown
    alert(`Order ${order.OrderNbr}\nProject Cost: ${this.formatCurrency(order.ProjectCost)}\nOn Hand: ${this.formatCurrency(order.OnHandValue)}\nMissing: ${this.formatCurrency(order.MissingValue)}`);
  }

  // Utility methods
  getStatusClass(status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'on hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'back order':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'shipping':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  formatDate(dateObj) {
    if (!dateObj || !dateObj.year) return 'N/A';

    try {
      const date = new Date(dateObj.year, dateObj.month - 1, dateObj.day);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', dateObj, error);
      return 'Invalid Date';
    }
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  formatNumber(value) {
    if (value === null || value === undefined || isNaN(value)) return '0';
    return parseFloat(value).toFixed(2);
  }

  formatCurrency(value) {
    if (value === null || value === undefined || isNaN(value)) {
      return this.displaySettings.showCurrency ? '$0.00' : '0.00';
    }
    
    if (this.displaySettings.showCurrency) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'CAD',
        minimumFractionDigits: 2
      }).format(value);
    } else {
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    }
  }

  escapeHtml(text) {
    if (!text || text === null || text === undefined) return '';

    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  showError(message) {
    console.error("Usage error:", message);

    this.container.innerHTML = `
      <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
        <div class="flex items-center">
          <div class="flex-shrink-0 text-red-500 dark:text-red-400">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
          </div>
        </div>
      </div>
    `;
  }
} 