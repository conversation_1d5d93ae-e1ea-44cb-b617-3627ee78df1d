// Sales Order Components component for MRP Dashboard
export class SalesOrderComponents {
  constructor(container, parentComponent) {
    console.log("🏗️ SalesOrderComponents Constructor");
    console.log("Container received:", !!container);
    console.log("Parent component received:", !!parentComponent);
    console.log("Parent component type:", parentComponent?.constructor?.name);
    console.log("Parent component sales orders:", parentComponent?.salesOrders?.length || 0);
    
    this.container = container;
    this.parentComponent = parentComponent;
    this.components = [];
    this.filteredComponents = [];
    this.currentPage = 1;
    this.itemsPerPage = 50; // Increased for better performance
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'OrderNbr';
    this.sortDirection = 'asc';
    this.isLoading = false;

    // Expansion state management
    this.expandedItems = new Set(); // Track which items are expanded

    // Database configuration for production orders (vlookup data)
    this.productionDbName = 'productionDb';
    this.productionStoreName = 'productionOrders';
    this.productionOrders = []; // Cache for production data

    // Database configuration for inventory (vlookup for cost analysis)
    this.inventoryDbName = 'inventoryDb';
    this.inventoryStoreName = 'inventoryItems';
    this.inventoryItems = []; // Cache for inventory data

    // Database configuration for sales orders (same as parent component)
    this.salesOrderDbName = 'salesOrderDb';
    this.salesOrderStoreName = 'salesOrders';
    
    console.log("🏗️ SalesOrderComponents Constructor Complete");
  }

  async init() {
    console.log("=== SALES ORDER COMPONENTS INIT STARTED ===");
    console.log("Container:", this.container);
    console.log("Parent component:", this.parentComponent);
    console.log("Parent component type:", this.parentComponent?.constructor?.name);
    console.log("Parent sales orders count:", this.parentComponent?.salesOrders?.length || 0);
    
    // Debug: Log the first few sales orders if they exist
    if (this.parentComponent?.salesOrders?.length > 0) {
      console.log("First sales order sample:", {
        OrderNbr: this.parentComponent.salesOrders[0].OrderNbr,
        LineItemCount: this.parentComponent.salesOrders[0].LineItemCount,
        HasLineItems: Array.isArray(this.parentComponent.salesOrders[0].LineItems),
        LineItemsLength: this.parentComponent.salesOrders[0].LineItems?.length || 0
      });
    }

    await this.refresh();
  }

  async refresh() {
    console.log("=== REFRESHING SALES ORDER COMPONENTS ===");

    this.isLoading = true;
    this.render();

    try {
      console.log("Loading production data for vlookup...");
      // First load production orders for vlookup
      await this.loadProductionOrders();
      
      console.log("Loading inventory data for cost analysis...");
      // Load inventory data for cost analysis
      await this.loadInventoryItems();
      
      console.log("Building hierarchical components table...");
      // Build hierarchical components table from sales orders and production data
      await this.buildComponentsTable();

      this.isLoading = false;
      this.render();
      console.log("=== SALES ORDER COMPONENTS REFRESH COMPLETED ===");
    } catch (error) {
      console.error("=== ERROR IN SALES ORDER COMPONENTS REFRESH ===");
      console.error("Error refreshing components:", error);
      console.error("Stack trace:", error.stack);
      this.isLoading = false;
      this.showError("Failed to refresh components: " + error.message);
      this.render();
    }
  }

  async loadProductionOrders() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.productionDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open production database for vlookup:", event.target.error);
        this.productionOrders = [];
        resolve(); // Continue without production data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.productionStoreName)) {
          console.warn("Production store not found, continuing without production data");
          this.productionOrders = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.productionStoreName], "readonly");
        const store = transaction.objectStore(this.productionStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.productionOrders = getAllRequest.result;
          console.log(`Loaded ${this.productionOrders.length} production orders for vlookup`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading production orders:", event.target.error);
          this.productionOrders = [];
          resolve(); // Continue without production data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async loadInventoryItems() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.inventoryDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open inventory database for cost analysis:", event.target.error);
        this.inventoryItems = [];
        resolve(); // Continue without inventory data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.inventoryStoreName)) {
          console.warn("Inventory store not found, continuing without inventory data");
          this.inventoryItems = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.inventoryStoreName], "readonly");
        const store = transaction.objectStore(this.inventoryStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.inventoryItems = getAllRequest.result;
          console.log(`Loaded ${this.inventoryItems.length} inventory items for cost analysis`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading inventory items:", event.target.error);
          this.inventoryItems = [];
          resolve(); // Continue without inventory data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async buildComponentsTable() {
    try {
      console.log("=== STARTING buildComponentsTable ===");
      console.log(`Sales orders available: ${this.parentComponent.salesOrders?.length || 0}`);
      console.log(`Production orders for vlookup: ${this.productionOrders.length}`);
      
      // Detailed debugging of parent component
      if (this.parentComponent) {
        console.log("Parent component details:", {
          hasParent: !!this.parentComponent,
          parentType: this.parentComponent.constructor?.name,
          hasSalesOrders: !!this.parentComponent.salesOrders,
          isArray: Array.isArray(this.parentComponent.salesOrders),
          salesOrdersLength: this.parentComponent.salesOrders?.length || 0,
          dataSource: this.parentComponent.dataSource,
          isLoading: this.parentComponent.isLoading
        });
        
        // Log first sales order if available
        if (this.parentComponent.salesOrders?.length > 0) {
          console.log("First sales order details:", {
            OrderNbr: this.parentComponent.salesOrders[0].OrderNbr,
            LineItemCount: this.parentComponent.salesOrders[0].LineItemCount,
            LineItems: this.parentComponent.salesOrders[0].LineItems?.length || 0
          });
        }
      } else {
        console.error("❌ Parent component is null or undefined!");
      }

      if (!this.parentComponent.salesOrders || this.parentComponent.salesOrders.length === 0) {
        console.warn("⚠️ No sales orders available for components table");
        console.log("Setting empty components arrays");
      this.components = [];
      this.filteredComponents = [];
        this.calculateTotalPages();
        return;
      }

      // Build hierarchical table: Sales Order → Line Items → Production → Operations → Materials
      console.log("🚀 Building hierarchical table...");
      const hierarchicalComponents = this.buildHierarchicalTable();
      
      this.components = hierarchicalComponents;
      this.filteredComponents = [...this.components];
      this.calculateTotalPages();
      this.applyFilters();

      console.log(`✅ Built hierarchical table with ${this.components.length} total rows`);

    } catch (error) {
      console.error("❌ Error building components table:", error);
      console.error("Stack trace:", error.stack);
      this.components = [];
      this.filteredComponents = [];
    }
  }

  getInventoryInfo(inventoryId) {
    if (!inventoryId || !this.inventoryItems.length) {
      return {
        lastCost: 0,
        qtyOnHand: 0,
        found: false
      };
    }

    const inventoryItem = this.inventoryItems.find(item => 
      item.InventoryID === inventoryId.trim()
    );

    if (inventoryItem) {
      return {
        lastCost: inventoryItem.LastCost || 0,
        qtyOnHand: inventoryItem.QtyOnHand || 0,
        found: true
      };
    }

    return {
      lastCost: 0,
      qtyOnHand: 0,
      found: false
    };
  }

  buildFlatItemsSummary(salesOrder) {
    console.log(`📊 Building flat items summary for order: ${salesOrder.OrderNbr}`);
    
    const flatItemsMap = new Map(); // Use Map to track quantities
    
    // Process each line item
    if (Array.isArray(salesOrder.LineItems)) {
      salesOrder.LineItems.forEach(lineItem => {
        // Check if this line item has production orders
        const hasProduction = lineItem.BOMItems && lineItem.BOMItems.some(bom => 
          bom.ProductionNbr && bom.ProductionNbr.trim()
        );
        
        if (hasProduction) {
          // Get materials from production orders
          const processedProductionNbrs = new Set();
          
          lineItem.BOMItems.forEach(bomItem => {
            if (bomItem.ProductionNbr && bomItem.ProductionNbr.trim() && 
                !processedProductionNbrs.has(bomItem.ProductionNbr.trim())) {
              processedProductionNbrs.add(bomItem.ProductionNbr.trim());
              
              // Find the production order
              const productionOrder = this.productionOrders.find(po => 
                po.MainProductionNbr === bomItem.ProductionNbr.trim()
              );
              
              if (productionOrder && Array.isArray(productionOrder.Materials)) {
                productionOrder.Materials.forEach(material => {
                  const inventoryId = material.InventoryID;
                  const quantity = material.QtyRequired || 0;
                  
                  if (inventoryId && inventoryId.trim()) {
                    const key = inventoryId.trim();
                    if (flatItemsMap.has(key)) {
                      flatItemsMap.get(key).totalQuantity += quantity;
                    } else {
                      flatItemsMap.set(key, {
                        inventoryId: key,
                        description: material.Description || '',
                        totalQuantity: quantity,
                        uom: material.UOM || ''
                      });
                    }
                  }
                });
              }
            }
          });
        } else {
          // No production - use the line item itself
          const inventoryId = lineItem.InventoryID;
          const quantity = lineItem.Quantity || 0;
          
          if (inventoryId && inventoryId.trim()) {
            const key = inventoryId.trim();
            if (flatItemsMap.has(key)) {
              flatItemsMap.get(key).totalQuantity += quantity;
            } else {
              flatItemsMap.set(key, {
                inventoryId: key,
                description: lineItem.LineDescription || '',
                totalQuantity: quantity,
                uom: lineItem.UOM || ''
              });
            }
          }
        }
      });
    }
    
    // Convert Map to array, add inventory info, and sort by inventory ID
    const flatItemsArray = Array.from(flatItemsMap.values())
      .map(item => {
        // Add inventory VLOOKUP info
        const inventoryInfo = this.getInventoryInfo(item.inventoryId);
        
        return {
          ...item,
          lastCost: inventoryInfo.lastCost,
          qtyOnHand: inventoryInfo.qtyOnHand,
          missing: Math.max(0, item.totalQuantity - inventoryInfo.qtyOnHand),
          found: inventoryInfo.found
        };
      })
      .sort((a, b) => a.inventoryId.localeCompare(b.inventoryId));
    
    console.log(`📊 Found ${flatItemsArray.length} unique flat items for order ${salesOrder.OrderNbr}`);
    return flatItemsArray;
  }

  buildHierarchicalTable() {
    console.log("🔨 Building hierarchical table...");
    
    const hierarchicalRows = [];
    let rowId = 0;

    // Process each sales order
    this.parentComponent.salesOrders.forEach((salesOrder, orderIndex) => {
      console.log(`📋 Processing sales order ${orderIndex + 1}/${this.parentComponent.salesOrders.length}: ${salesOrder.OrderNbr}`);
      
      // 1. SALES ORDER ROW (Level 0) - Show only once per order
      const salesOrderRow = {
        id: `row-${++rowId}`,
        type: 'sales_order',
        level: 0,
        OrderNbr: salesOrder.OrderNbr, // Only show for first row of this order
        displayOrderNbr: salesOrder.OrderNbr, // Always store the order number
        Status: salesOrder.Status,
        ItemDescription: `Sales Order - ${salesOrder.LineItemCount} items`,
        Quantity: salesOrder.OrderedQty || 0,
        UOM: '',
        UnitPrice: 0,
        ExtAmount: salesOrder.OrderTotal || 0,
        ProductionNbr: '',
        OperationNbr: '',
        WorkCenter: '',
        hasChildren: salesOrder.LineItems && salesOrder.LineItems.length > 0,
        isFirstRowOfOrder: true,
        isExpandable: true,
        isExpanded: false,
        parentId: null
      };
      hierarchicalRows.push(salesOrderRow);
      
      // 2. LINE ITEMS (Level 1) - Process each line item
      if (Array.isArray(salesOrder.LineItems)) {
        salesOrder.LineItems.forEach((lineItem, lineIndex) => {
          console.log(`  📦 Processing line item ${lineIndex + 1}/${salesOrder.LineItems.length}: ${lineItem.InventoryID}`);
          
          const lineItemRow = {
            id: `row-${++rowId}`,
            type: 'line_item',
            level: 1,
            OrderNbr: '', // Empty for line items - don't repeat order number
            displayOrderNbr: salesOrder.OrderNbr, // Store for reference
            Status: salesOrder.Status,
            ItemDescription: `${lineItem.InventoryID} - ${lineItem.LineDescription}`,
          Quantity: lineItem.Quantity || 0,
          UOM: lineItem.UOM || '',
          UnitPrice: lineItem.UnitPrice || 0,
          ExtAmount: lineItem.ExtAmount || 0,
            ProductionNbr: '',
            OperationNbr: '',
            WorkCenter: '',
            hasChildren: lineItem.BOMItems && lineItem.BOMItems.length > 0,
            isFirstRowOfOrder: false,
            isExpandable: lineItem.BOMItems && lineItem.BOMItems.length > 0,
            isExpanded: false,
            parentId: salesOrderRow.id
          };
          hierarchicalRows.push(lineItemRow);
          
          // 3. PRODUCTION ORDERS IMMEDIATELY AFTER THIS LINE ITEM (Level 2)
          if (Array.isArray(lineItem.BOMItems) && lineItem.BOMItems.length > 0) {
            // Process each BOM item that has a production number
            const processedProductionNbrs = new Set(); // Avoid duplicates
            
            lineItem.BOMItems.forEach((bomItem, bomIndex) => {
              if (bomItem.ProductionNbr && bomItem.ProductionNbr.trim() && !processedProductionNbrs.has(bomItem.ProductionNbr.trim())) {
                processedProductionNbrs.add(bomItem.ProductionNbr.trim());
                console.log(`    🏭 VLOOKUP for production: ${bomItem.ProductionNbr} (for line item ${lineItem.InventoryID})`);
                
                // VLOOKUP: Find matching production order from production.js data
                const productionOrder = this.productionOrders.find(po => 
                  po.MainProductionNbr === bomItem.ProductionNbr.trim()
                );
                
                if (productionOrder) {
                  console.log(`    ✅ Found production order: ${productionOrder.MainProductionNbr} - adding immediately after line item`);
                  
                  // Production Order Row (Level 2) - IMMEDIATELY after this line item
                  const productionRow = {
                    id: `row-${++rowId}`,
                    type: 'production_order',
                    level: 2,
                    OrderNbr: '', // Empty - don't repeat order number
                    displayOrderNbr: salesOrder.OrderNbr,
                    Status: productionOrder.MainStatus,
                    ItemDescription: `Production ${productionOrder.MainProductionNbr} - ${productionOrder.MainDescription}`,
                    Quantity: productionOrder.MainQty || 0,
                    UOM: productionOrder.MainUOM || '',
                    UnitPrice: 0,
                    ExtAmount: productionOrder.TotalMaterialCost || 0,
                    ProductionNbr: productionOrder.MainProductionNbr,
                    OperationNbr: '',
                    WorkCenter: '',
                    hasChildren: (productionOrder.Operations && productionOrder.Operations.length > 0) || 
                                (productionOrder.Materials && productionOrder.Materials.length > 0),
                    isFirstRowOfOrder: false,
                    parentLineItem: lineItem.InventoryID, // Track which line item this belongs to
                    isExpandable: (productionOrder.Operations && productionOrder.Operations.length > 0) || 
                                 (productionOrder.Materials && productionOrder.Materials.length > 0),
                    isExpanded: false,
                    parentId: lineItemRow.id
                  };
                  hierarchicalRows.push(productionRow);
                  
                  // 4. OPERATIONS (Level 3) - Under this production order
                  if (Array.isArray(productionOrder.Operations) && productionOrder.Operations.length > 0) {
                    productionOrder.Operations.forEach((operation, opIndex) => {
                      console.log(`      🔧 Adding operation: ${operation.OperationNbr} for production ${productionOrder.MainProductionNbr}`);
                      
                      const operationRow = {
                        id: `row-${++rowId}`,
                        type: 'operation',
                        level: 3,
                        OrderNbr: '', // Empty
                        displayOrderNbr: salesOrder.OrderNbr,
                        Status: operation.Status || productionOrder.MainStatus,
                        ItemDescription: `Operation ${operation.OperationNbr} - ${operation.OperationDescription}`,
                        Quantity: 0,
                        UOM: '',
                        UnitPrice: 0,
                        ExtAmount: 0,
                        ProductionNbr: productionOrder.MainProductionNbr,
                        OperationNbr: operation.OperationNbr,
                        WorkCenter: operation.WorkCenter || '',
                        hasChildren: false,
                        isFirstRowOfOrder: false,
                        isExpandable: false,
                        isExpanded: false,
                        parentId: productionRow.id
                      };
                      hierarchicalRows.push(operationRow);
                    });
                  }
                  
                  // 5. MATERIALS (Level 3) - Under this production order
                  if (Array.isArray(productionOrder.Materials) && productionOrder.Materials.length > 0) {
                    productionOrder.Materials.forEach((material, matIndex) => {
                      console.log(`      📦 Adding material: ${material.InventoryID} for operation ${material.OperationNbr} (production ${productionOrder.MainProductionNbr})`);
                      
                      const materialRow = {
                        id: `row-${++rowId}`,
                        type: 'material',
                        level: 3,
                        OrderNbr: '', // Empty
                        displayOrderNbr: salesOrder.OrderNbr,
                        Status: productionOrder.MainStatus,
                        ItemDescription: `Material ${material.InventoryID} - ${material.Description}`,
        Quantity: material.QtyRequired || 0,
        UOM: material.UOM || '',
        UnitPrice: material.UnitCost || 0,
                        ExtAmount: material.TotalCost || 0,
                        ProductionNbr: productionOrder.MainProductionNbr,
        OperationNbr: material.OperationNbr || '',
                        WorkCenter: '',
                        hasChildren: false,
                        isFirstRowOfOrder: false,
                        isExpandable: false,
                        isExpanded: false,
                        parentId: productionRow.id
                      };
                      hierarchicalRows.push(materialRow);
                    });
                  }
                  
                } else {
                  console.log(`    ❌ No production order found for: ${bomItem.ProductionNbr} (for line item ${lineItem.InventoryID})`);
                  
                  // Add placeholder for missing production data - IMMEDIATELY after this line item
                  const missingProductionRow = {
                    id: `row-${++rowId}`,
                    type: 'missing_production',
                    level: 2,
                    OrderNbr: '', // Empty
                    displayOrderNbr: salesOrder.OrderNbr,
                    Status: 'Not Found',
                    ItemDescription: `Production ${bomItem.ProductionNbr} - Data not available`,
                    Quantity: 0,
                    UOM: '',
                    UnitPrice: 0,
                    ExtAmount: 0,
                    ProductionNbr: bomItem.ProductionNbr,
                    OperationNbr: '',
                    WorkCenter: '',
                    hasChildren: false,
                    isFirstRowOfOrder: false,
                    parentLineItem: lineItem.InventoryID,
                    isExpandable: false,
                    isExpanded: false,
                    parentId: lineItemRow.id
                  };
                  hierarchicalRows.push(missingProductionRow);
                }
              }
            });
          }
        });
      }
    });

    console.log(`🎯 Hierarchical table complete: ${hierarchicalRows.length} total rows`);
    return hierarchicalRows;
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredComponents.length / this.itemsPerPage));

    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  toggleExpansion(componentId) {
    console.log('Toggling expansion for:', componentId);
    
    // Find the component
    const component = this.components.find(c => c.id === componentId);
    if (!component || !component.isExpandable) {
      console.log('Component not found or not expandable');
      return;
    }
    
    // Toggle the expansion state
    if (this.expandedItems.has(componentId)) {
      this.expandedItems.delete(componentId);
      component.isExpanded = false;
      console.log('Collapsed:', componentId);
    } else {
      this.expandedItems.add(componentId);
      component.isExpanded = true;
      console.log('Expanded:', componentId);
    }
    
    // Re-apply filters to update the view
    this.applyFilters();
  }

  expandAll() {
    console.log('Expanding all components');
    
    // Clear the expanded items set and rebuild it
    this.expandedItems.clear();
    
    // Add all expandable components to the expanded set
    this.components.forEach(component => {
      if (component.isExpandable) {
        this.expandedItems.add(component.id);
        component.isExpanded = true;
      }
    });
    
    // Re-apply filters to update the view
    this.applyFilters();
  }

  collapseAll() {
    console.log('Collapsing all components');
    
    // Clear all expanded items
    this.expandedItems.clear();
    
    // Set all components to collapsed
    this.components.forEach(component => {
      if (component.isExpandable) {
        component.isExpanded = false;
      }
    });
    
    // Re-apply filters to update the view
    this.applyFilters();
  }

  isComponentVisible(component) {
    // Sales orders (level 0) are always visible
    if (component.level === 0) {
      return true;
    }
    
    // For other levels, check if all parent levels are expanded
    return this.isParentExpanded(component);
  }

  isParentExpanded(component) {
    if (!component.parentId) {
      return true; // No parent, so it's visible
    }
    
    const parent = this.components.find(c => c.id === component.parentId);
    if (!parent) {
      return false; // Parent not found
    }
    
    // Check if this parent is expanded and if its parent is also expanded (recursive)
    return parent.isExpanded && this.isParentExpanded(parent);
  }

  applyFilters() {
    let filtered = [...this.components];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(comp => {
        return (
          (comp.displayOrderNbr || comp.OrderNbr || '').toLowerCase().includes(searchLower) ||
          (comp.ItemDescription || '').toLowerCase().includes(searchLower) ||
          (comp.ProductionNbr || '').toLowerCase().includes(searchLower) ||
          (comp.OperationNbr || '').toLowerCase().includes(searchLower) ||
          (comp.WorkCenter || '').toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply visibility filter based on expansion state
    filtered = filtered.filter(comp => this.isComponentVisible(comp));

    // Apply sorting - Maintain strict hierarchical order
    filtered.sort((a, b) => {
      // First sort by displayOrderNbr (actual order number)
      const aOrderNbr = a.displayOrderNbr || a.OrderNbr || '';
      const bOrderNbr = b.displayOrderNbr || b.OrderNbr || '';
      
      if (aOrderNbr !== bOrderNbr) {
        return aOrderNbr.localeCompare(bOrderNbr);
      }

      // Within the same order, maintain strict hierarchy by using the original row ID
      // This preserves the order: Sales Order -> Line Item -> Production -> Operations -> Materials
      const aRowId = parseInt(a.id.replace('row-', '')) || 0;
      const bRowId = parseInt(b.id.replace('row-', '')) || 0;
      
      return aRowId - bRowId;
    });

    this.filteredComponents = filtered;
    this.calculateTotalPages();
    this.currentPage = 1; // Reset to first page when filtering
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;

    tableContainer.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading components data...</p>
      </div>
    `;
  }

  renderContent() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;

    console.log("Rendering components content:");
    console.log("- Total components:", this.components.length);
    console.log("- Filtered components:", this.filteredComponents.length);
    console.log("- Parent sales orders:", this.parentComponent.salesOrders ? this.parentComponent.salesOrders.length : 0);

    // Calculate pagination
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredComponents.length);
    const pageComponents = this.filteredComponents.slice(startIndex, endIndex);

    tableContainer.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <!-- Components Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 table-fixed">
            <colgroup>
              <col class="w-16"> <!-- Order # -->
              <col class="w-48"> <!-- Item / Description (smaller width for truncation) -->
              <col class="w-20"> <!-- Status -->
              <col class="w-20"> <!-- Quantity -->
              <col class="w-20"> <!-- Unit Price -->
              <col class="w-24"> <!-- Extended -->
              <col class="w-24"> <!-- Production # -->
              <col class="w-24"> <!-- Order Total -->
              <col class="w-24"> <!-- Ship Date -->
              <col class="w-32"> <!-- Flat Item -->
            </colgroup>
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderNbr">
                  Order # <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Item / Description
                </th>
                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Quantity">
                  Quantity <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="UnitPrice">
                  Unit Price <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ExtAmount">
                  Extended <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Production #
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Order Total
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Ship Date
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Flat Item
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderHierarchicalRows(pageComponents)}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 p-4 space-y-3 sm:space-y-0">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Showing ${Math.min(startIndex + 1, this.filteredComponents.length)} to 
            ${Math.min(endIndex, this.filteredComponents.length)} of 
            ${this.filteredComponents.length} results
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentPage} of ${this.totalPages}
            </span>
            
            <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      </div>
    `;

    this.setupComponentsEventListeners();
  }

  renderHierarchicalRows(components) {
    if (!components || components.length === 0) {
      return `
        <tr>
          <td colspan="10" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
            <div class="flex flex-col items-center">
              <svg class="w-12 h-12 mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <p class="text-lg font-medium mb-2">No components found</p>
            <p class="text-sm">Load sales orders first to view the component breakdown</p>
            </div>
          </td>
        </tr>
      `;
    }

    return components.map(component => {
      const indentLevel = component.level || 0;
      const indentPx = indentLevel * 24; // 24px per level for better visibility
      
      // Get appropriate icons and styling based on component type
      const typeInfo = this.getComponentTypeInfo(component);
      
      return `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 ${indentLevel > 0 ? 'bg-gray-25 dark:bg-gray-850' : ''}">
          <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
            ${component.isFirstRowOfOrder ? this.escapeHtml(component.displayOrderNbr) : ''}
          </td>
          <td class="px-2 py-3 text-sm overflow-hidden">
            <div style="margin-left: ${indentPx}px" class="flex items-center max-w-full">
              ${component.isExpandable ? `
                <button class="expand-toggle mr-2 p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors" data-id="${component.id}" title="${component.isExpanded ? 'Collapse' : 'Expand'}">
                  <i class="fas ${component.isExpanded ? 'fa-chevron-down' : 'fa-chevron-right'} text-gray-500 dark:text-gray-400 text-xs"></i>
                </button>
              ` : `
                <div class="w-6 mr-2"></div>
              `}
              ${typeInfo.icon}
              <div class="ml-2 flex-1 min-w-0 max-w-full overflow-hidden">
                <div class="truncate ${typeInfo.textClass} max-w-full overflow-hidden text-ellipsis whitespace-nowrap" title="${this.escapeHtml(component.ItemDescription)}">
                  ${this.escapeHtml(this.truncateText(component.ItemDescription, 45))}
                </div>
                ${component.isExpandable ? `
                  <span class="mt-1 inline-flex items-center px-2 py-1 text-xs font-medium ${component.isExpanded ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'}">
                    <i class="fas ${component.isExpanded ? 'fa-minus' : 'fa-plus'} mr-1"></i>
                    ${component.isExpanded ? 'Expanded' : 'Collapsed'}
                </span>
              ` : ''}
              </div>
            </div>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-center">
            <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(component.Status)}">
              ${this.escapeHtml(component.Status || 'Unknown')}
            </span>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
            ${this.formatNumber(component.Quantity)} ${this.escapeHtml(component.UOM || '')}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
            ${component.UnitPrice ? this.formatCurrency(component.UnitPrice) : ''}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
            ${this.formatCurrency(component.ExtAmount)}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
            ${component.ProductionNbr ? `
              <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200">
                <i class="fas fa-industry mr-1"></i>
                ${this.escapeHtml(component.ProductionNbr)}
              </span>
            ` : ''}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
            ${this.formatCurrency(this.getOrderTotalForOrder(component.displayOrderNbr))}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
            ${this.getShipDateForOrder(component.displayOrderNbr)}
          </td>
          <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
            ${(() => {
              const flatItems = this.getFlatItemContent(component);
              if (flatItems) {
                return flatItems; // Return the HTML directly (button for sales orders)
              }
              return '';
            })()}
          </td>
        </tr>
      `;
    }).join('');
  }

  getFlatItemContent(component) {
    switch (component.type) {
      case 'sales_order':
        // Show a view button for sales orders to view flat items in modal
        return this.createFlatItemsViewButton(component);
      case 'line_item':
      case 'production_order':
      case 'operation':
      case 'material':
      case 'missing_production':
      default:
        return ''; // Other rows don't show flat items
    }
  }

  createFlatItemsViewButton(component) {
    return `
      <button 
        class="view-flat-items-btn inline-flex items-center px-2 py-1 text-xs bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-800 dark:text-blue-200 rounded-md transition-colors"
        data-order-nbr="${this.escapeHtml(component.displayOrderNbr)}"
        title="View flat items for this sales order"
      >
        <i class="fas fa-eye mr-1"></i>
        View Items
      </button>
    `;
  }




  getComponentTypeInfo(component) {
    switch (component.type) {
      case 'sales_order':
        return {
          icon: '<i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400"></i>',
          textClass: 'font-bold text-blue-900 dark:text-blue-100'
        };
      case 'line_item':
        return {
          icon: '<i class="fas fa-box-open text-green-600 dark:text-green-400"></i>',
          textClass: 'font-semibold text-green-800 dark:text-green-200'
        };
      case 'production_order':
          return {
          icon: '<i class="fas fa-industry text-purple-600 dark:text-purple-400"></i>',
          textClass: 'font-medium text-purple-800 dark:text-purple-200'
          };
      case 'operation':
          return {
          icon: '<i class="fas fa-cogs text-orange-600 dark:text-orange-400"></i>',
            textClass: 'text-orange-800 dark:text-orange-200'
          };
      case 'material':
        return {
          icon: '<i class="fas fa-cube text-gray-600 dark:text-gray-400"></i>',
          textClass: 'text-gray-800 dark:text-gray-200'
        };
      case 'missing_production':
        return {
          icon: '<i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>',
          textClass: 'text-red-800 dark:text-red-200'
        };
      default:
        return {
          icon: '<i class="fas fa-circle text-gray-600 dark:text-gray-400"></i>',
          textClass: 'text-gray-800 dark:text-gray-200'
        };
    }
  }

  getStatusClass(status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'on hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'back order':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'shipping':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'planned':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'in process':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'canceled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'not found':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  setupComponentsEventListeners() {
    // Pagination event handlers
    const firstPageBtn = document.getElementById('first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.render();
        }
      });
    }

    const prevPageBtn = document.getElementById('prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    const nextPageBtn = document.getElementById('next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    const lastPageBtn = document.getElementById('last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.render();
        }
      });
    }

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
      });
    });

    // Expand/Collapse buttons
    const expandButtons = document.querySelectorAll('.expand-toggle');
    expandButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        const componentId = button.getAttribute('data-id');
        if (componentId) {
          this.toggleExpansion(componentId);
          // Re-render to update the view
          this.render();
        }
      });
    });

    // View Flat Items buttons
    const viewFlatItemsButtons = document.querySelectorAll('.view-flat-items-btn');
    viewFlatItemsButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        const orderNbr = button.getAttribute('data-order-nbr');
        if (orderNbr) {
          this.showFlatItemsModal(orderNbr);
        }
      });
    });

    console.log('Components event listeners set up successfully');
  }

  async showFlatItemsModal(orderNbr) {
    console.log(`Showing flat items modal for order: ${orderNbr}`);
    
    // Find the sales order
    const salesOrder = this.parentComponent.salesOrders.find(order => 
      order.OrderNbr === orderNbr
    );
    
    if (!salesOrder) {
      console.error(`Sales order ${orderNbr} not found`);
      return;
    }

    // Build flat items summary
    const flatItems = this.buildFlatItemsSummary(salesOrder);
    
    const modalContent = `
      <div class="modal-content overflow-y-auto" style="max-height: 70vh;">
        <div class="p-6">
          <div class="flex items-center justify-between pb-3 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              <i class="fas fa-layer-group mr-2 text-amber-600"></i>
              Flat Items for Sales Order ${this.escapeHtml(orderNbr)}
            </h3>
            <button id="flat-items-close-button" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>

        <div class="mt-4">
          <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4">
            <div class="flex items-center text-blue-800 dark:text-blue-200">
              <i class="fas fa-info-circle mr-2"></i>
              <span class="text-sm">
                This list shows all the actual inventory items needed for consumption for this sales order.
              </span>
            </div>
          </div>

          ${flatItems.length > 0 ? `
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                  Consolidated Parts List with Cost Analysis (${flatItems.length} unique items)
                </h4>
              </div>
              
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Inventory ID
                      </th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Description
                      </th>
                      <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Total Qty
                      </th>
                      <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Cost
                      </th>
                      <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Qty On Hand
                      </th>
                      <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Missing
                      </th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        UOM
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    ${flatItems.map((item, index) => `
                      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center">
                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium ${item.found ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'} rounded-full">
                              <i class="fas ${item.found ? 'fa-cube' : 'fa-exclamation-triangle'} mr-1"></i>
                              ${this.escapeHtml(item.inventoryId)}
                            </span>
                          </div>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
                          <div class="max-w-xs truncate" title="${this.escapeHtml(item.description)}">
                            ${this.escapeHtml(item.description || 'No description')}
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white text-right">
                          <span class="font-medium">${this.formatNumber(item.totalQuantity)}</span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white text-right">
                          <span class="${item.found ? 'text-gray-900 dark:text-white' : 'text-red-600 dark:text-red-400'}">${this.formatCurrency(item.lastCost)}</span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-right">
                          <span class="font-medium ${item.qtyOnHand >= item.totalQuantity ? 'text-green-600 dark:text-green-400' : item.qtyOnHand > 0 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'}">${this.formatNumber(item.qtyOnHand)}</span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-right">
                          <span class="font-medium ${item.missing > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}">${this.formatNumber(item.missing)}</span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          ${this.escapeHtml(item.uom || '')}
                        </td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
              
              ${(() => {
                const totalProjectCost = flatItems.reduce((sum, item) => sum + (item.totalQuantity * item.lastCost), 0);
                const totalOnHandValue = flatItems.reduce((sum, item) => sum + (item.qtyOnHand * item.lastCost), 0);
                const totalMissingValue = flatItems.reduce((sum, item) => sum + (item.missing * item.lastCost), 0);
                const totalQty = flatItems.reduce((sum, item) => sum + item.totalQuantity, 0);
                const totalOnHandQty = flatItems.reduce((sum, item) => sum + item.qtyOnHand, 0);
                const totalMissingQty = flatItems.reduce((sum, item) => sum + item.missing, 0);
                
                return `
                  <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-t border-gray-200 dark:border-gray-600">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div class="text-center">
                        <div class="text-gray-600 dark:text-gray-400 mb-1">Project Cost</div>
                        <div class="font-bold text-lg text-gray-900 dark:text-white">${this.formatCurrency(totalProjectCost)}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">${this.formatNumber(totalQty)} total units</div>
                      </div>
                      <div class="text-center">
                        <div class="text-gray-600 dark:text-gray-400 mb-1">On Hand Value</div>
                        <div class="font-bold text-lg ${totalOnHandValue >= totalProjectCost ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}">${this.formatCurrency(totalOnHandValue)}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">${this.formatNumber(totalOnHandQty)} available units</div>
                      </div>
                      <div class="text-center">
                        <div class="text-gray-600 dark:text-gray-400 mb-1">Missing Value</div>
                        <div class="font-bold text-lg ${totalMissingValue > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}">${this.formatCurrency(totalMissingValue)}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">${this.formatNumber(totalMissingQty)} missing units</div>
                      </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                      <div class="text-xs text-gray-600 dark:text-gray-400">
                        <span class="font-medium">${flatItems.length}</span> unique parts • 
                        <span class="font-medium">${flatItems.filter(item => item.found).length}</span> found in inventory • 
                        <span class="font-medium">${flatItems.filter(item => !item.found).length}</span> not found
                      </div>
                    </div>
                  </div>
                `;
              })()}
            </div>
          ` : `
            <div class="text-center py-8">
              <div class="text-gray-400 dark:text-gray-500 mb-4">
                <i class="fas fa-inbox text-4xl"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Flat Items Found</h3>
              <p class="text-gray-500 dark:text-gray-400">
                This sales order doesn't have any identifiable inventory items for consumption.
              </p>
            </div>
          `}
        </div>

          <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
            <button id="flat-items-close-button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
              Close
            </button>
          </div>
        </div>
      </div>
    `;

    // Use the modal system from the parent component  
    const modalController = this.createModal('flat-items-modal', modalContent, 'max-w-6xl');
    
    // Wait for modal to close
    await modalController.promise;
  }

  // Use the same modal creation system as the parent component
  createModal(modalId, modalContent, width = 'max-w-4xl') {
    // Remove any existing modal with the same ID
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.id = modalId;

    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = `bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full ${width} h-auto max-h-[80vh]`;
    modalContainer.innerHTML = modalContent;

    // Append to DOM
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);

    // Create object to hold our result data and controller functions
    const modalController = {
      modalContainer,
      modalOverlay,
      result: null,
      setResult: function(result) {
        this.result = result;
      }
    };

    // Create a promise that resolves when the modal is closed
    const modalPromise = new Promise((resolve) => {
      // Function to close the modal
      const closeModal = (result) => {
        const modal = document.getElementById(modalId);
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }

        // Remove global event listener
        document.removeEventListener('keydown', handleEscKey);

        // Save result and resolve promise
        modalController.setResult(result);
        resolve(result);
      };

      // Event handler for Escape key
      const handleEscKey = (event) => {
        if (event.key === 'Escape') {
          closeModal({action: 'cancel'});
        }
      };

      // Add global escape key handler
      document.addEventListener('keydown', handleEscKey);

      // Click outside to close
      modalOverlay.addEventListener('click', (event) => {
        if (event.target === modalOverlay) {
          closeModal({action: 'cancel'});
        }
      });

      // Find and attach event listeners to all buttons in the modal
      const buttons = modalContainer.querySelectorAll('button');
      buttons.forEach(button => {
        button.addEventListener('click', (event) => {
          event.preventDefault();
          event.stopPropagation();

          // Close modal
          closeModal({
            action: 'close',
            button: button.id
          });
        });
      });

      // Add closeModal method to controller
      modalController.closeModal = closeModal;
    });

    // Attach the promise to the controller
    modalController.promise = modalPromise;

    // Return the controller with container and promise
    return modalController;
  }

  // Utility methods for formatting
  formatNumber(value) {
    if (value === null || value === undefined || isNaN(value)) return '0';
    return parseFloat(value).toFixed(2);
  }

  getShipDateForOrder(orderNbr) {
    if (!orderNbr || !this.parentComponent || !this.parentComponent.salesOrders) {
      return '';
    }

    const salesOrder = this.parentComponent.salesOrders.find(order => 
      order.OrderNbr === orderNbr
    );

    if (!salesOrder || !salesOrder.ShippingDate) {
      return '';
    }

    return this.formatDate(salesOrder.ShippingDate);
  }

  getOrderTotalForOrder(orderNbr) {
    if (!orderNbr || !this.parentComponent || !this.parentComponent.salesOrders) {
      return 0;
    }

    const salesOrder = this.parentComponent.salesOrders.find(order => 
      order.OrderNbr === orderNbr
    );

    if (!salesOrder || typeof salesOrder.OrderTotal !== 'number') {
      return 0;
    }

    return salesOrder.OrderTotal;
  }

  formatCurrency(value) {
    if (value === null || value === undefined || isNaN(value)) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  }

  formatDate(dateObj) {
    if (!dateObj || !dateObj.year) return 'N/A';

    try {
      const date = new Date(dateObj.year, dateObj.month - 1, dateObj.day);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', dateObj, error);
      return 'Invalid Date';
    }
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  escapeHtml(text) {
    if (!text || text === null || text === undefined) return '';

    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  truncateText(text, maxLength = 45) {
    if (!text || text === null || text === undefined) return '';
    
    const str = text.toString();
    if (str.length <= maxLength) return str;
    
    return str.substring(0, maxLength) + '...';
  }

  showError(message) {
    console.error("Components error:", message);

    const tableContainer = document.getElementById('sales-table-container');
    if (tableContainer) {
      tableContainer.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    }
  }
}
