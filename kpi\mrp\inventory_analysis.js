// Inventory Analysis component for MRP Dashboard
import { connectionManager } from '../../core/connection.js';
import { InventoryAllocationComponent } from './inventory_allocation.js';

export class InventoryAnalysisComponent {
  constructor(container) {
    this.container = container;
    this.inventoryItems = [];
    this.filteredItems = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'InventoryID';
    this.sortDirection = 'asc';
    this.filterStatus = 'all';
    this.isLoading = true;
    this.dbName = 'inventoryDb';
    this.storeName = 'inventoryItems';
    this.settingsStoreName = 'appSettings';
    this.dataSource = null; // 'acumatica', 'indexedDB', or 'sample'
    this.lastSyncTime = null;
    this.warehouseFilter = 'all'; // Which warehouse to filter by
    this.availableWarehouses = []; // List of available warehouses

    // Add tab management similar to sales_order.js
    this.currentTab = 'inventory'; // 'inventory', 'allocation'
    this.allocationComponent = null;
  }

  async init() {
    console.log("Initializing Inventory Analysis component");
    
    // Only use a single loading indicator
    this.isLoading = true;
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Load inventory data
      await this.loadData();

      // Initialize allocation component after data is loaded
      if (this.inventoryItems.length > 0) {
        this.allocationComponent = new InventoryAllocationComponent(this.container, this);
      }
    
      // Update loading state and render again
      this.isLoading = false;
      this.render();
    
      // Set up event listeners
      this.setupEventListeners();
    } catch (error) {
      console.error("Error initializing inventory analysis:", error);
      this.isLoading = false;
      this.showError("Failed to initialize: " + error.message);
      this.render();
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      // Try to open with current version first (don't upgrade yet)
      const checkRequest = indexedDB.open(this.dbName);
      
      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        const currentVersion = db.version;
        db.close();
        
        // Now open with the correct version based on what we found
        const version = this.settingsStoreName && !db.objectStoreNames.contains(this.settingsStoreName) ? 
          Math.max(currentVersion + 1, 2) : currentVersion;
        
        const request = indexedDB.open(this.dbName, version);

        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open inventory database"));
        };

        request.onsuccess = (event) => {
          console.log("Successfully opened inventory database");
          resolve();
        };

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Create object store for inventory items if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });
            
            // Create indices for common search and sort operations
            store.createIndex("InventoryID", "InventoryID", { unique: true });
            store.createIndex("Description", "Description", { unique: false });
            store.createIndex("ItemStatus", "ItemStatus", { unique: false });
            store.createIndex("LastCost", "LastCost", { unique: false });
            store.createIndex("QtyOnHand", "QtyOnHand", { unique: false });
            store.createIndex("ReorderPoint", "ReorderPoint", { unique: false });
            store.createIndex("SafetyStock", "SafetyStock", { unique: false });
            store.createIndex("VendorName", "VendorName", { unique: false });
          }
          
          // Create object store for app settings if it doesn't exist
          if (!db.objectStoreNames.contains(this.settingsStoreName)) {
            db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
          }
          
          console.log("Inventory database schema upgraded to version", db.version);
        };
      };
      
      checkRequest.onerror = (event) => {
        console.error("Error checking database version:", event.target.error);
        reject(new Error("Could not check database version"));
      };
    });
  }

  async loadData(forceRefresh = false) {
    try {
      this.isLoading = true;
      
      // Check connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      console.log("Acumatica connection status:", connectionStatus.acumatica);
      
      // When forceRefresh is true, always try to fetch from Acumatica first if connected
      if (forceRefresh && connectionStatus.acumatica.isConnected) {
        console.log("Force refreshing - fetching latest inventory data from Acumatica");
        try {
          const result = await this.fetchAcumaticaInventory(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            console.log("Successfully fetched data from Acumatica");
            this.inventoryItems = this.parseAcumaticaInventory(result.data);
            console.log(`Parsed ${this.inventoryItems.length} inventory items`);
            
            // Update data source and sync time
            this.dataSource = 'acumatica';
            this.lastSyncTime = new Date().toLocaleString();
            
            // Store in IndexedDB for offline access
            await this.storeInventoryInIndexedDB(this.inventoryItems);
            
            // Also store the sync info in settings
            await this.saveSettings({
              lastSyncTime: this.lastSyncTime
            });
            
            console.log(`Stored ${this.inventoryItems.length} inventory items in IndexedDB`);
            
            // Continue with filtering
            this.filteredItems = [...this.inventoryItems];
            this.calculateTotalPages();
            this.isLoading = false;
            return;
          } else {
            console.warn("Error refreshing from Acumatica:", result.error);
            // Fall through to IndexedDB fallback
          }
        } catch (fetchError) {
          console.error("Error refreshing inventory from Acumatica:", fetchError);
          // Fall through to IndexedDB fallback
        }
      }
      
      // If not forcing refresh or Acumatica refresh failed, follow normal loading flow
      // If not connected to Acumatica, or if offline and we have data in IndexedDB
      if (!connectionStatus.acumatica.isConnected || (!forceRefresh && !navigator.onLine)) {
        // Try to get data from IndexedDB
        console.log("Attempting to fetch data from IndexedDB...");
        this.inventoryItems = await this.getInventoryFromIndexedDB();
        
        // Also try to get the last sync time
        const settings = await this.loadSettings();
        if (settings && settings.lastSyncTime) {
          this.lastSyncTime = settings.lastSyncTime;
        }
        
        // If we have data in IndexedDB, use it
        if (this.inventoryItems.length > 0) {
          console.log(`Retrieved ${this.inventoryItems.length} inventory items from IndexedDB`);
          this.dataSource = 'indexedDB';
          this.filteredItems = [...this.inventoryItems];
          this.calculateTotalPages();
          this.isLoading = false;
          return;
        }
      } else {
        // Connected to Acumatica, fetch real data
        console.log("Fetching inventory from Acumatica");
        try {
          const result = await this.fetchAcumaticaInventory(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.inventoryItems = this.parseAcumaticaInventory(result.data);
            
            // Update data source and sync time
            this.dataSource = 'acumatica';
            this.lastSyncTime = new Date().toLocaleString();
            
            await this.storeInventoryInIndexedDB(this.inventoryItems);
            
            // Also store the sync info in settings
            await this.saveSettings({
              lastSyncTime: this.lastSyncTime
            });
            
            console.log(`Stored ${this.inventoryItems.length} inventory items in IndexedDB`);
            this.filteredItems = [...this.inventoryItems];
            this.calculateTotalPages();
            this.isLoading = false;
            return;
          } else {
            // If error fetching from Acumatica, try IndexedDB
            console.warn("Error fetching from Acumatica, trying IndexedDB:", result.error);
            this.inventoryItems = await this.getInventoryFromIndexedDB();
            
            if (this.inventoryItems.length > 0) {
              console.log(`Retrieved ${this.inventoryItems.length} inventory items from IndexedDB`);
              this.dataSource = 'indexedDB';
              this.filteredItems = [...this.inventoryItems];
              this.calculateTotalPages();
              this.isLoading = false;
              return;
            }
          }
        } catch (fetchError) {
          console.error("Error fetching inventory from Acumatica:", fetchError);
          // Try to get data from IndexedDB as fallback
          this.inventoryItems = await this.getInventoryFromIndexedDB();
          
          if (this.inventoryItems.length > 0) {
            console.log(`Retrieved ${this.inventoryItems.length} inventory items from IndexedDB`);
            this.dataSource = 'indexedDB';
            this.filteredItems = [...this.inventoryItems];
            this.calculateTotalPages();
            this.isLoading = false;
            return;
          }
        }
      }
      
      // If we reach here, we couldn't get data from either Acumatica or IndexedDB
      this.inventoryItems = [];
      this.filteredItems = [];
      this.dataSource = 'empty';
      this.calculateTotalPages();
      this.isLoading = false;
      this.showError("No inventory data available. Please connect to Acumatica to fetch data.");
      
    } catch (error) {
      console.error('Error loading inventory data:', error);
      this.inventoryItems = [];
      this.filteredItems = [];
      this.dataSource = 'empty';
      this.calculateTotalPages();
      this.isLoading = false;
      this.showError("Error loading inventory data: " + error.message);
    }
  }

  async fetchAcumaticaInventory(instance) {
    try {
      if (!instance) {
        const connectionStatus = connectionManager.getConnectionStatus();
        if (!connectionStatus.acumatica.instance) {
          throw new Error('No Acumatica instance URL available. Please check connection.');
        }
        instance = connectionStatus.acumatica.instance;
      }
      
      // Build Acumatica API URL for inventory
      const apiUrl = `${instance}/entity/Enventbridge/22.200.001/InventoryQty?$expand=WarehouseQty`;
      
      console.log("Fetching inventory with URL:", apiUrl);
      
      // Make request with cookies through the connection manager
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include'  // Include cookies for authentication
      });
      
      // Log the response status for debugging
      console.log("Acumatica API response status:", response.status);
      
      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          // Update connection status in connection manager
          connectionManager.connections.acumatica.isConnected = false;
          if (chrome?.storage?.local) {
            await chrome.storage.local.set({ 'connections': connectionManager.connections });
          }
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }
        throw new Error(`Failed to fetch inventory: ${response.status} ${response.statusText}`);
      }
      
      // Parse response
      const data = await response.json();
      console.log(`Received ${data.length} inventory items from Acumatica API`);
      return { success: true, data };
    } catch (error) {
      console.error("Error fetching inventory from Acumatica:", error);
      return { success: false, error: error.message };
    }
  }

  parseAcumaticaInventory(inventoryData) {
    try {
      console.log("Parsing Acumatica inventory data, raw sample:", inventoryData[0]);
      // Process inventory data from Acumatica
      return inventoryData.map(item => {
        // Ensure we have a unique ID
        const id = item.id || `inv-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
        
        // Extract main inventory fields
        const inventoryId = item.InventoryID?.value || '';
        const description = item.Description?.value || '';
        const itemStatus = item.ItemStatus?.value || '';
        const lastCost = parseFloat(item.LastCost?.value || 0);
        const leadTimeDays = parseInt(item.LeadTimeDays?.value || 0);
        const reorderPoint = parseFloat(item.ReorderPoint?.value || 0);
        const safetyStock = parseFloat(item.SafetyStock?.value || 0);
        const vendorName = item.VendorName?.value || '';
        
        // Process warehouse quantities
        const warehouseQty = Array.isArray(item.WarehouseQty) 
          ? item.WarehouseQty.map(wh => ({
              id: wh.id || `wh-${id}-${Math.random().toString(36).substring(2, 10)}`,
              Warehouse: wh.Warehouse?.value || '',
              QtyOnHand: parseFloat(wh.QtyOnHand?.value || 0)
            })) 
          : [];
        
        // Calculate total qty on hand
        const totalQtyOnHand = warehouseQty.reduce((sum, wh) => sum + wh.QtyOnHand, 0);
        
        // Determine stock status
        let stockStatus = 'Normal';
        if (totalQtyOnHand <= 0) {
          stockStatus = 'Out of Stock';
        } else if (totalQtyOnHand < safetyStock) {
          stockStatus = 'Critical';
        } else if (totalQtyOnHand < reorderPoint) {
          stockStatus = 'Low Stock';
        }
        
        // Parse into our standard inventory format
        return {
          id,
          InventoryID: inventoryId,
          Description: description,
          ItemStatus: itemStatus,
          LastCost: lastCost,
          LeadTimeDays: leadTimeDays,
          ReorderPoint: reorderPoint,
          SafetyStock: safetyStock,
          VendorName: vendorName,
          WarehouseQty: warehouseQty,
          QtyOnHand: totalQtyOnHand,
          StockStatus: stockStatus
        };
      });
    } catch (error) {
      console.error("Error parsing Acumatica inventory:", error);
      return [];
    }
  }

  async getInventoryFromIndexedDB() {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database:", event.target.error);
        reject(new Error("Could not open inventory database"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for reading, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          resolve([]);
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const inventoryItems = getAllRequest.result;
          console.log(`Retrieved ${inventoryItems.length} inventory items from IndexedDB`);
          resolve(inventoryItems);
        };
        
        getAllRequest.onerror = (event) => {
          console.error("Error retrieving inventory:", event.target.error);
          reject(new Error("Failed to retrieve inventory from database"));
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error retrieving inventory:", event.target.error);
          db.close();
        };
      };
      
      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during read operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  async storeInventoryInIndexedDB(inventoryItems) {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database for storing:", event.target.error);
        reject(new Error("Could not open inventory database for storing"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for writing, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          reject(new Error("Inventory store not found for storing"));
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readwrite");
        const store = transaction.objectStore(this.storeName);
        let count = 0;
        let errorCount = 0;
        const totalItems = inventoryItems.length;
        
        // Clear existing data if this is a fresh load
        store.clear().onsuccess = () => {
           console.log(`Cleared existing inventory data. Storing ${totalItems} new items.`);
           if (totalItems === 0) {
               resolve(); // Nothing more to do
               db.close();
               return;
           }

          // Add each inventory item
          inventoryItems.forEach(item => {
            // Make sure the id exists to prevent duplicate key errors
            if (!item.id) {
              // Generate a unique ID if one doesn't exist
              item.id = `inv-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
            }
            
            // Use put instead of add to replace items with same key instead of error
            const putRequest = store.put(item);
            
            putRequest.onsuccess = () => {
              count++;
              if (count + errorCount === totalItems) {
                console.log(`Successfully stored ${count} inventory items.`);
                db.close();
                resolve();
              }
            };
            
            putRequest.onerror = (event) => {
              console.error("Error storing inventory item:", item.InventoryID, event.target.error);
              errorCount++;
              if (count + errorCount === totalItems) {
                 console.warn(`Finished storing with ${errorCount} errors.`);
                 db.close();
                 resolve();
              }
            };
          });
        };
        
        store.clear().onerror = (event) => {
          console.error("Error clearing existing inventory data:", event.target.error);
          db.close();
          reject(new Error("Failed to clear existing inventory"));
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error storing inventory:", event.target.error);
          db.close();
          reject(new Error("Failed to store inventory"));
        };
      };
      
      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during write operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  generateSampleData() {
    console.warn("Sample data generation is disabled");
    return [];
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredItems.length / this.itemsPerPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading inventory data...</p>
      </div>
    `;
  }

  renderContent() {
    // Add CSS for expandable tab labels, scoped to the inventory component
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      /* Scope styles to the inventory component header */
      .inventory-component-header .tab-btn { 
        position: relative;
        overflow: hidden;
        transition: width 0.3s ease;
        width: 40px; /* Keep initial width small */
        min-width: 40px;
      }
      .inventory-component-header .tab-btn.active {
        width: auto; /* Expand active tab */
      }
      .inventory-component-header .tab-btn:hover {
        width: auto; /* Expand on hover */
      }
      .inventory-component-header .tab-label {
        opacity: 0;
        max-width: 0;
        overflow: hidden;
        white-space: nowrap;
        transition: all 0.3s ease;
        margin-left: 0;
      }
      .inventory-component-header .tab-btn.active .tab-label,
      .inventory-component-header .tab-btn:hover .tab-label {
        opacity: 1;
        max-width: 100px; /* Adjust max-width as needed */
        margin-left: 6px;
      }
    `;
    document.head.appendChild(styleEl);

    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        ${this.renderDataSourceNotice()}
        <div class="inventory-component-header flex flex-col md:flex-row justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">
            ${this.currentTab === 'inventory' ? 'Inventory Analysis' :
              this.currentTab === 'allocation' ? 'Inventory Allocation' :
              'Inventory Management'}
          </h2>

          <div class="flex flex-wrap items-center gap-2">
            <div class="relative">
              <input type="text" id="inventory-search" placeholder="Search inventory..." class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm" value="${this.searchTerm || ''}">
              <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <select id="stock-status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Items</option>
              <option value="normal" ${this.filterStatus === 'normal' ? 'selected' : ''}>Normal</option>
              <option value="low stock" ${this.filterStatus === 'low stock' ? 'selected' : ''}>Low Stock</option>
              <option value="critical" ${this.filterStatus === 'critical' ? 'selected' : ''}>Critical</option>
              <option value="out of stock" ${this.filterStatus === 'out of stock' ? 'selected' : ''}>Out of Stock</option>
            </select>

            <div class="flex gap-2">
              <!-- Inventory Button -->
              <button id="inventory-button" class="tab-btn p-2 ${this.currentTab === 'inventory' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Inventory">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Inventory</span>
              </button>

              <!-- Allocation Button -->
              <button id="allocation-button" class="tab-btn p-2 ${this.currentTab === 'allocation' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Allocation">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Allocation</span>
              </button>

              <!-- Date Range Button -->
              <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Date Range">
                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  <circle cx="12" cy="14" r="0.5" stroke="currentColor" stroke-width="2"></circle>
                </svg>
              </button>

              <!-- Refresh Button -->
              <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>

              <!-- Export Button -->
              <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
              </button>

              <!-- Settings Button -->
              <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Settings">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div id="inventory-table-container">
          <!-- Content will be rendered by renderActiveView -->
        </div>
      </div>
    `;

    this.setupEventListeners();
    this.renderActiveView();
  }

  renderActiveView() {
    const tableContainer = document.getElementById('inventory-table-container');
    if (!tableContainer) return;

    // Clear the table container
    tableContainer.innerHTML = '';

    if (this.currentTab === 'inventory') {
      this.renderInventoryView();
    } else if (this.currentTab === 'allocation') {
      this.renderAllocationView();
    }
  }

  renderInventoryView() {
    const tableContainer = document.getElementById('inventory-table-container');
    if (!tableContainer) return;

    tableContainer.innerHTML = `
      <!-- Inventory Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="InventoryID">
                Item # <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Description">
                Description <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="QtyOnHand">
                Qty On Hand <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Available
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Allocated
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ReorderPoint">
                Reorder Point <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="LastCost">
                Last Cost <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="StockStatus">
                Status <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${this.renderTableRows()}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredItems.length)} to 
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredItems.length)} of 
          ${this.filteredItems.length} results
        </div>
        
        <div class="flex items-center space-x-1">
          <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>
          
          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>
          
          <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;

    // Set up table-specific event listeners
    this.setupEventListeners();
  }

  renderAllocationView() {
    const tableContainer = document.getElementById('inventory-table-container');
    if (!tableContainer) return;

    console.log("=== RENDERING ALLOCATION VIEW ===");
    console.log("Inventory items available:", this.inventoryItems.length);
    console.log("Allocation component exists:", !!this.allocationComponent);

    // Always refresh or create allocation component when switching to this tab
    if (this.inventoryItems.length > 0) {
      if (!this.allocationComponent) {
        console.log("Creating new InventoryAllocationComponent instance");
        this.allocationComponent = new InventoryAllocationComponent(tableContainer, this);
        // Set the container for the allocation component to only use the table container
        this.allocationComponent.containerOnly = true;
        this.allocationComponent.init();
      } else {
        console.log("Refreshing existing InventoryAllocationComponent instance");
        // Update the container reference to ensure it uses only the table container
        this.allocationComponent.container = tableContainer;
        this.allocationComponent.containerOnly = true;
        // Refresh the allocation component with latest data
        this.allocationComponent.refresh();
      }
    } else {
      console.log("No inventory items available, showing message");
      // Show message when no data available
      tableContainer.innerHTML = `
        <div class="flex flex-col items-center justify-center p-8">
          <div class="text-gray-600 dark:text-gray-400">
            <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
            </svg>
            <p class="text-center">No inventory data available for allocation management</p>
            <p class="text-center text-sm mt-2">
              ${this.dataSource === 'empty' ? 'Click refresh to load inventory from Acumatica' : 'Load inventory first to view allocations'}
            </p>
            <button class="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md" onclick="window.location.reload()">
              Refresh Page
            </button>
          </div>
        </div>
      `;
    }
  }

  switchTab(tabName) {
    if (this.currentTab === tabName) return; // Already on this tab

    this.currentTab = tabName;

    // Update the header title
    const headerTitle = document.querySelector('h2');
    if (headerTitle) {
      if (this.currentTab === 'inventory') {
        headerTitle.textContent = 'Inventory Analysis';
      } else if (this.currentTab === 'allocation') {
        headerTitle.textContent = 'Inventory Allocation';
      }
    }

    // Update button states
    this.updateButtonStates();

    // Render the active view first
    this.renderActiveView();
    
    // Then synchronize search term after components are rendered
    setTimeout(() => {
      this.synchronizeSearchTerm();
    }, 100);
  }

  // Synchronize search term across all components
  synchronizeSearchTerm() {
    const searchInput = document.getElementById('inventory-search');
    if (searchInput && this.searchTerm !== searchInput.value.trim()) {
      this.searchTerm = searchInput.value.trim();
    }

    // Apply search term to allocation component using the proper method
    if (this.allocationComponent && typeof this.allocationComponent.updateSearchTerm === 'function') {
      this.allocationComponent.updateSearchTerm(this.searchTerm);
    }
  }

  updateButtonStates() {
    const inventoryButton = document.getElementById('inventory-button');
    const allocationButton = document.getElementById('allocation-button');

    if (inventoryButton && allocationButton) {
      // First remove active class from all buttons
      inventoryButton.classList.remove('bg-blue-600', 'text-white', 'active');
      inventoryButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');

      allocationButton.classList.remove('bg-blue-600', 'text-white', 'active');
      allocationButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');

      // Then add active class to the correct button
      if (this.currentTab === 'inventory') {
        inventoryButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        inventoryButton.classList.add('bg-blue-600', 'text-white', 'active');
      } else if (this.currentTab === 'allocation') {
        allocationButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        allocationButton.classList.add('bg-blue-600', 'text-white', 'active');
      }
    }
  }

  renderDataSourceNotice() {
    if (this.dataSource === 'acumatica') {
      return `
        <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4 dark:bg-green-900 dark:border-green-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-green-500 dark:text-green-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-green-700 dark:text-green-200">
                Displaying live data from Acumatica. Total items: ${this.inventoryItems.length}
              </p>
            </div>
          </div>
        </div>
      `;
    }
    return '';
  }

  renderTableRows() {
    if (this.filteredItems.length === 0) {
      return `
        <tr>
          <td colspan="9" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
            No inventory items found. Click the refresh button to load data.
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredItems.length);
    const displayedItems = this.filteredItems.slice(start, end);

    return displayedItems.map(item => {
      // Calculate allocation data (will be implemented in allocation component)
      const allocatedQty = this.getAllocatedQuantity(item.InventoryID);
      const availableQty = Math.max(0, item.QtyOnHand - allocatedQty);

      return `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
          <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
            ${this.escapeHtml(item.InventoryID)}
          </td>
          <td class="px-3 py-4 text-sm text-gray-800 dark:text-gray-200">
            <div class="line-clamp-2">${this.escapeHtml(item.Description)}</div>
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
            ${item.QtyOnHand.toFixed(2)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right">
            <span class="font-medium ${availableQty > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
              ${availableQty.toFixed(2)}
            </span>
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right">
            <span class="${allocatedQty > 0 ? 'text-orange-600 dark:text-orange-400' : 'text-gray-500 dark:text-gray-400'}">
              ${allocatedQty.toFixed(2)}
            </span>
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
            ${item.ReorderPoint.toFixed(2)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
            ${this.formatCurrency(item.LastCost)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-center">
            <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(item.StockStatus)}">
              ${this.escapeHtml(item.StockStatus)}
            </span>
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button data-id="${item.id}" class="view-item text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
              <i class="fas fa-eye"></i>
            </button>
          </td>
        </tr>
      `;
    }).join('');
  }

  // Get allocated quantity for an inventory item (placeholder - will be implemented with allocation component)
  getAllocatedQuantity(inventoryId) {
    // This will be implemented with the allocation system
    // For now, return 0
    if (this.allocationComponent && this.allocationComponent.getAllocatedQuantity) {
      return this.allocationComponent.getAllocatedQuantity(inventoryId);
    }
    return 0;
  }

  setupEventListeners() {
    // Add a small delay to ensure DOM is fully rendered
    setTimeout(() => {
      // Tab buttons
      const inventoryButton = document.getElementById('inventory-button');
      if (inventoryButton) {
        inventoryButton.addEventListener('click', () => {
          this.switchTab('inventory');
        });
      }

      const allocationButton = document.getElementById('allocation-button');
      if (allocationButton) {
        allocationButton.addEventListener('click', () => {
          this.switchTab('allocation');
        });
      }

      const dateRangeButton = document.getElementById('date-range-button');
      if (dateRangeButton) {
        dateRangeButton.addEventListener('click', () => {
          this.showDateRangePicker();
        });
      }

      // Search input
      const searchInput = document.getElementById('inventory-search');
      if (searchInput) {
        searchInput.addEventListener('input', this.debounce(() => {
          this.searchTerm = searchInput.value.trim();
          this.currentPage = 1;
          
          // Update clear button visibility
          const clearSearchBtn = document.getElementById('clear-search');
          if (clearSearchBtn) {
            if (this.searchTerm) {
              clearSearchBtn.classList.remove('hidden');
            } else {
              clearSearchBtn.classList.add('hidden');
            }
          }
          
          // Apply search to current tab
          if (this.currentTab === 'inventory') {
            this.applyFilters();
          } else if (this.currentTab === 'allocation' && this.allocationComponent) {
            this.allocationComponent.updateSearchTerm(this.searchTerm);
          }
        }, 300));
      }

    // Clear search
    const clearSearchBtn = document.getElementById('clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        
        // Hide clear button
        clearSearchBtn.classList.add('hidden');
        
        // Apply search clearing to current tab
        if (this.currentTab === 'inventory') {
          this.applyFilters();
        } else if (this.currentTab === 'allocation' && this.allocationComponent) {
          this.allocationComponent.updateSearchTerm('');
        }
      });
    }

    // Status filter
    const statusFilter = document.getElementById('stock-status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', () => {
        this.filterStatus = statusFilter.value;
        this.currentPage = 1;
        this.applyFilters();
      });
    }
    
    // Refresh button
    const refreshButton = document.getElementById('refresh-button');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        refreshButton.disabled = true;
        refreshButton.innerHTML = `
          <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        `;
        
        this.loadData(true)
          .then(() => {
            console.log("Inventory data refreshed successfully");
            alert("Inventory data refreshed successfully");
            refreshButton.disabled = false;
            refreshButton.innerHTML = `
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            `;
          })
          .catch(error => {
            console.error("Error refreshing data:", error);
            alert("Error refreshing data: " + error.message);
            refreshButton.disabled = false;
            refreshButton.innerHTML = `
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            `;
          });
      });
    }

    // Export button
    const exportButton = document.getElementById('export-button');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        this.exportInventoryData();
      });
    }

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
      });
    });

    // Pagination event handlers
    const firstPageBtn = document.getElementById('first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.render();
        }
      });
    }

    const prevPageBtn = document.getElementById('prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    const nextPageBtn = document.getElementById('next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    const lastPageBtn = document.getElementById('last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.render();
        }
      });
    }

    // View item buttons
    const viewButtons = document.querySelectorAll('.view-item');
    viewButtons.forEach(button => {
      button.addEventListener('click', () => {
        const itemId = button.getAttribute('data-id');
        this.viewItemDetails(itemId);
      });
    });

          // Settings button
      const settingsButton = document.getElementById('settings-button');
      if (settingsButton) {
        settingsButton.addEventListener('click', () => {
          this.showSettings();
        });
      }

      console.log('All event listeners have been set up');
    }, 50); // Small delay to ensure DOM is ready
  }

  applyFilters() {
    // Filter by search term
    let filtered = this.inventoryItems;
    
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(item => 
        (item.InventoryID?.toLowerCase().includes(term)) ||
        (item.Description?.toLowerCase().includes(term)) ||
        (item.VendorName?.toLowerCase().includes(term))
      );
    }
    
    // Filter by status
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(item => 
        item.StockStatus?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }
    
    // Filter by warehouse
    if (this.warehouseFilter !== 'all') {
      filtered = filtered.filter(item => {
        if (!item.WarehouseQty || !Array.isArray(item.WarehouseQty)) {
          return false;
        }
        
        // Check if this item has the selected warehouse
        return item.WarehouseQty.some(wh => 
          wh.Warehouse === this.warehouseFilter && wh.QtyOnHand > 0
        );
      });
    }
    
    // Sort the data
    filtered.sort((a, b) => {
      let comparison = 0;
      const fieldA = a[this.sortField];
      const fieldB = b[this.sortField];

      // Handle null/undefined values
      const valA = fieldA ?? '';
      const valB = fieldB ?? '';

      switch (this.sortField) {
        case 'InventoryID':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'Description':
        case 'StockStatus':
        case 'VendorName':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'QtyOnHand':
        case 'ReorderPoint':
        case 'SafetyStock':
        case 'LastCost':
          comparison = parseFloat(valA) - parseFloat(valB);
          break;
        default:
          comparison = String(valA).localeCompare(String(valB));
      }

      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    this.filteredItems = filtered;
    this.calculateTotalPages();
    this.render();
  }

  viewItemDetails(itemId) {
    const item = this.inventoryItems.find(i => i.id === itemId);
    if (!item) return;
    
    // Build the modal content
    const modalContent = `
      <div class="modal-content overflow-y-auto" style="max-height: 70vh;">
        <div class="p-6">
          <div class="flex flex-col md:flex-row justify-between mb-6">
            <h2 class="text-xl font-semibold mb-2">Inventory Item Details</h2>
            <div class="flex items-center">
              <span class="px-2 py-1 text-sm font-semibold rounded-full ${this.getStatusClass(item.StockStatus)}">
                ${this.escapeHtml(item.StockStatus)}
              </span>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Item Details -->
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Item Information</h3>
              <div class="space-y-2">
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Item #:</span> 
                  <span>${this.escapeHtml(item.InventoryID)}</span>
                </p>
                <p class="text-sm">
                  <span class="font-medium">Description:</span><br> 
                  <span>${this.escapeHtml(item.Description)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Item Status:</span> 
                  <span>${this.escapeHtml(item.ItemStatus)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Last Cost:</span> 
                  <span>${this.formatCurrency(item.LastCost)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Lead Time (Days):</span> 
                  <span>${item.LeadTimeDays}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Vendor:</span> 
                  <span>${this.escapeHtml(item.VendorName)}</span>
                </p>
              </div>
            </div>

            <!-- Quantity Details -->
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Quantity Information</h3>
              <div class="space-y-2">
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Total Qty On Hand:</span> 
                  <span>${item.QtyOnHand.toFixed(2)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Safety Stock:</span> 
                  <span>${item.SafetyStock.toFixed(2)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Reorder Point:</span> 
                  <span>${item.ReorderPoint.toFixed(2)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Stock Status:</span> 
                  <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(item.StockStatus)}">
                    ${this.escapeHtml(item.StockStatus)}
                  </span>
                </p>
              </div>
            </div>
          </div>

          <!-- Warehouse Quantities -->
          <div class="mt-6">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Warehouse Quantities</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Warehouse</th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty On Hand</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  ${item.WarehouseQty.length > 0 ? item.WarehouseQty.map(wh => `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(wh.Warehouse)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${wh.QtyOnHand.toFixed(2)}</td>
                    </tr>
                  `).join('') : `
                    <tr>
                      <td colspan="2" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
                        No warehouse quantity data available
                      </td>
                    </tr>
                  `}
                </tbody>
              </table>
            </div>
          </div>

          <!-- Buttons -->
          <div class="flex justify-end gap-2 mt-6">
            <button id="close-item-modal" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Close
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Create modal
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.id = 'item-details-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl h-auto max-h-[80vh]';
    modalContainer.style.cssText = 'display: flex; flex-direction: column;';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Function to close the modal
    const closeModal = () => {
      // Make sure the modal is still in the DOM before removing
      const modal = document.getElementById('item-details-modal');
      if (modal && modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
    };
    
    // Setup event listeners - add a small delay to ensure DOM is ready
    setTimeout(() => {
      // Get the close button with its new ID
      const closeButton = document.getElementById('close-item-modal');
      if (closeButton) {
        closeButton.addEventListener('click', closeModal);
      }
      
      // Also close when clicking outside the modal
      modalOverlay.addEventListener('click', (event) => {
        // Only close if clicked directly on the overlay, not its children
        if (event.target === modalOverlay) {
          closeModal();
        }
      });
      
      // Add keyboard event for Escape key
      document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
          closeModal();
        }
      }, { once: true });
    }, 50);
  }

  exportInventoryData() {
    try {
      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';
      
      // Add header row
      csvContent += 'Item #,Description,Qty On Hand,Safety Stock,Reorder Point,Last Cost,Stock Status,Vendor\n';
      
      // Add each row of data
      this.filteredItems.forEach(item => {
        const row = [
          item.InventoryID,
          item.Description,
          item.QtyOnHand.toFixed(2),
          item.SafetyStock.toFixed(2),
          item.ReorderPoint.toFixed(2),
          item.LastCost.toFixed(2),
          item.StockStatus,
          item.VendorName
        ].map(cell => `"${cell?.toString().replace(/"/g, '""')}"`).join(',');
        
        csvContent += row + '\n';
      });
      
      // Create download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `inventory_${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      
      // Trigger download
      link.click();
      document.body.removeChild(link);
      
      console.log("Inventory data exported successfully");
      alert("Inventory data exported successfully");
    } catch (error) {
      console.error('Error exporting data:', error);
      alert("Error exporting data: " + error.message);
    }
  }

  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case 'normal':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'low stock':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'out of stock':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    }
  }

  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      currencyDisplay: 'symbol'
    }).format(value);
  }

  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'inventory-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('inventory-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    } else {
      console.error("Container not available to show error:", message);
    }
  }

  async saveSettings(settings) {
    try {
      // Open database
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for saving settings:", event.target.error);
          reject(new Error("Could not open database for saving settings"));
        };
      });
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.error("Settings store not found, cannot save settings");
        db.close();
        return;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readwrite");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Save inventory settings
      store.put({
        id: "inventorySettings",
        ...settings
      });
      
      await new Promise((resolve, reject) => {
        transaction.oncomplete = () => {
          resolve();
        };
        transaction.onerror = (event) => {
          reject(event.target.error);
        };
      });
      
      db.close();
      return true;
    } catch (error) {
      console.error("Error saving settings:", error);
      return false;
    }
  }

  async loadSettings() {
    try {
      // Open database
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for loading settings:", event.target.error);
          reject(new Error("Could not open database for loading settings"));
        };
      });
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.log("Settings store not found, using defaults");
        db.close();
        return null;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readonly");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Get inventory settings
      const inventorySettings = await new Promise((resolve) => {
        const request = store.get("inventorySettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading inventory settings:", event.target.error);
          resolve(null);
        };
      });
      
      // Apply settings if they exist
      if (inventorySettings) {
        if (inventorySettings.itemsPerPage) {
          this.itemsPerPage = inventorySettings.itemsPerPage;
        }
        
        if (inventorySettings.warehouseFilter) {
          this.warehouseFilter = inventorySettings.warehouseFilter;
        }
        
        if (inventorySettings.sortField) {
          this.sortField = inventorySettings.sortField;
        }
        
        if (inventorySettings.sortDirection) {
          this.sortDirection = inventorySettings.sortDirection;
        }
        
        if (inventorySettings.lastSyncTime) {
          this.lastSyncTime = inventorySettings.lastSyncTime;
        }
      }
      
      db.close();
      return inventorySettings;
    } catch (error) {
      console.error("Error loading settings:", error);
      return null;
    }
  }

  showSettings() {
    // Build list of warehouses from inventory items
    this.availableWarehouses = this.getAvailableWarehouses();
    
    const settingsHtml = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Inventory Settings</h3>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Items per page</label>
          <select id="settings-items-per-page" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="5" ${this.itemsPerPage === 5 ? 'selected' : ''}>5</option>
            <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
            <option value="25" ${this.itemsPerPage === 25 ? 'selected' : ''}>25</option>
            <option value="50" ${this.itemsPerPage === 50 ? 'selected' : ''}>50</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Warehouse filter</label>
          <select id="settings-warehouse-filter" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="all" ${this.warehouseFilter === 'all' ? 'selected' : ''}>All Warehouses</option>
            ${this.availableWarehouses.map(warehouse => 
              `<option value="${warehouse}" ${this.warehouseFilter === warehouse ? 'selected' : ''}>${warehouse}</option>`
            ).join('')}
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Default sort</label>
          <select id="settings-default-sort" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="InventoryID" ${this.sortField === 'InventoryID' ? 'selected' : ''}>Item #</option>
            <option value="Description" ${this.sortField === 'Description' ? 'selected' : ''}>Description</option>
            <option value="QtyOnHand" ${this.sortField === 'QtyOnHand' ? 'selected' : ''}>Qty On Hand</option>
            <option value="ReorderPoint" ${this.sortField === 'ReorderPoint' ? 'selected' : ''}>Reorder Point</option>
            <option value="SafetyStock" ${this.sortField === 'SafetyStock' ? 'selected' : ''}>Safety Stock</option>
            <option value="LastCost" ${this.sortField === 'LastCost' ? 'selected' : ''}>Last Cost</option>
            <option value="StockStatus" ${this.sortField === 'StockStatus' ? 'selected' : ''}>Status</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Sort direction</label>
          <div class="flex gap-4">
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="asc" ${this.sortDirection === 'asc' ? 'checked' : ''} class="mr-2">
              Ascending
            </label>
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="desc" ${this.sortDirection === 'desc' ? 'checked' : ''} class="mr-2">
              Descending
            </label>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-6">
          <button id="settings-cancel-button" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Cancel
          </button>
          <button id="settings-save-button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Save Changes
          </button>
        </div>
      </div>
    `;
    
    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'settings-modal-overlay';
    
    const modalContent = document.createElement('div');
    modalContent.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContent.innerHTML = settingsHtml;
    
    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);
    
    // Give DOM time to update
    setTimeout(() => {
      // Setup event listeners for the modal buttons
      const cancelButton = document.getElementById('settings-cancel-button');
      const saveButton = document.getElementById('settings-save-button');
      
      const closeModal = () => {
        const modal = document.getElementById('settings-modal-overlay');
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      };
      
      if (cancelButton) {
        cancelButton.addEventListener('click', closeModal);
      }
      
      if (saveButton) {
        saveButton.addEventListener('click', async () => {
          // Get settings values
          const itemsPerPageSelect = document.getElementById('settings-items-per-page');
          const warehouseFilterSelect = document.getElementById('settings-warehouse-filter');
          const defaultSortSelect = document.getElementById('settings-default-sort');
          const sortDirectionRadios = document.getElementsByName('settings-sort-direction');
          
          if (itemsPerPageSelect) {
            this.itemsPerPage = parseInt(itemsPerPageSelect.value);
          }
          
          if (warehouseFilterSelect) {
            this.warehouseFilter = warehouseFilterSelect.value;
          }
          
          if (defaultSortSelect) {
            this.sortField = defaultSortSelect.value;
          }
          
          let selectedDirection = 'desc';
          sortDirectionRadios.forEach(radio => {
            if (radio.checked) {
              selectedDirection = radio.value;
            }
          });
          this.sortDirection = selectedDirection;
          
          // Save settings to IndexedDB
          await this.saveSettings({
            itemsPerPage: this.itemsPerPage,
            warehouseFilter: this.warehouseFilter,
            sortField: this.sortField,
            sortDirection: this.sortDirection
          });
          
          // Apply settings and re-render
          this.calculateTotalPages();
          this.applyFilters();
          
          // Close modal
          closeModal();
        });
      }
      
      // Close on click outside
      modalOverlay.addEventListener('click', (event) => {
        if (event.target === modalOverlay) {
          closeModal();
        }
      });
      
      // Close on escape key
      document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
          closeModal();
        }
      }, { once: true });
    }, 50);
  }
  
  // Get all available warehouses from inventory items
  getAvailableWarehouses() {
    const warehouses = new Set();
    
    this.inventoryItems.forEach(item => {
      if (item.WarehouseQty && Array.isArray(item.WarehouseQty)) {
        item.WarehouseQty.forEach(wh => {
          if (wh.Warehouse) {
            warehouses.add(wh.Warehouse);
          }
        });
      }
    });
    
    return [...warehouses].sort();
  }

  showDateRangePicker() {
    // Create date range picker modal
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);
    
    // Format dates for input fields
    const formatDateForInput = (date) => {
      if (!date) return '';
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };
    
    const startDateValue = formatDateForInput(oneMonthAgo);
    const endDateValue = formatDateForInput(today);
    
    const modalContent = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Select Date Range</h3>
        
        <div class="mb-6">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Filter inventory items by their creation date or last updated date. Only items within this date range will be displayed.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Start Date</label>
              <input 
                type="date" 
                id="date-range-start" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${startDateValue}"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium mb-1">End Date</label>
              <input 
                type="date" 
                id="date-range-end" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${endDateValue}"
              >
            </div>
          </div>
        </div>
        
        <div class="flex justify-between">
          <div>
            <button id="date-range-clear" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md">
              Clear Filter
            </button>
          </div>
          <div class="flex gap-2">
            <button id="date-range-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Cancel
            </button>
            <button id="date-range-apply" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
              Apply Filter
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'date-range-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners for the modal
    const cancelButton = document.getElementById('date-range-cancel');
    const applyButton = document.getElementById('date-range-apply');
    const clearButton = document.getElementById('date-range-clear');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (cancelButton) {
      cancelButton.addEventListener('click', closeModal);
    }
    
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        console.log('Date filter cleared');
        closeModal();
      });
    }
    
    if (applyButton) {
      applyButton.addEventListener('click', () => {
        const startDate = document.getElementById('date-range-start').value;
        const endDate = document.getElementById('date-range-end').value;
        
        console.log('Date range applied:', startDate, 'to', endDate);
        closeModal();
      });
    }
    
    // Add keyboard event for Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        closeModal();
      }
    }, { once: true });
  }
}
